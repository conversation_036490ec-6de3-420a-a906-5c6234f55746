"""
SFF Production & Quality Management System
Inventory Management GUI
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QLineEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QComboBox, QTextEdit,
    QMessageBox, QHeaderView, QFrame, QSplitter, QGroupBox, QDateEdit,
    QDoubleSpinBox, QSpinBox
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from database.db_manager import DatabaseManager
from models.data_models import Material
from utils.validators import DataValidator
from config.settings import THEME_COLOR, ERROR_COLOR, SUCCESS_COLOR
from decimal import Decimal
from datetime import datetime

class InventoryManagement(QWidget):
    """Inventory Management interface"""
    
    def __init__(self, auth_manager):
        super().__init__()
        self.auth_manager = auth_manager
        self.db_manager = DatabaseManager()
        self.current_material = None
        
        self.init_ui()
        self.load_materials()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Inventory Management")
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet(self.get_stylesheet())
        
        # Main layout
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Material list
        left_panel = self.create_material_list_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Material details
        right_panel = self.create_material_details_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([700, 500])
        
        main_layout.addWidget(splitter)
    
    def create_material_list_panel(self):
        """Create material list panel"""
        panel = QFrame()
        panel.setObjectName("listPanel")
        layout = QVBoxLayout(panel)
        layout.setSpacing(10)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Materials Inventory")
        title_label.setObjectName("panelTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Add new material button
        if self.auth_manager.has_permission('inventory_all') or self.auth_manager.has_permission('all'):
            self.add_button = QPushButton("Add New Material")
            self.add_button.setObjectName("primaryButton")
            self.add_button.clicked.connect(self.add_new_material)
            header_layout.addWidget(self.add_button)
        
        layout.addLayout(header_layout)
        
        # Search and filter
        search_layout = QHBoxLayout()
        
        search_label = QLabel("Search:")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search by name or code...")
        self.search_input.textChanged.connect(self.filter_materials)
        search_layout.addWidget(self.search_input)
        
        type_label = QLabel("Type:")
        search_layout.addWidget(type_label)
        
        self.type_filter = QComboBox()
        self.type_filter.addItems(["All Types", "RAW_INGREDIENT", "FLAVOR", "CHEMICAL", "PACKAGING"])
        self.type_filter.currentTextChanged.connect(self.filter_materials)
        search_layout.addWidget(self.type_filter)
        
        layout.addLayout(search_layout)
        
        # Materials table
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(7)
        self.materials_table.setHorizontalHeaderLabels([
            "Code", "Name", "Type", "Stock", "Min Threshold", "Unit Cost", "Status"
        ])
        
        # Set column widths
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        
        self.materials_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.materials_table.setAlternatingRowColors(True)
        self.materials_table.itemSelectionChanged.connect(self.on_material_selected)
        
        layout.addWidget(self.materials_table)
        
        # Low stock warning
        self.low_stock_label = QLabel()
        self.low_stock_label.setObjectName("warningLabel")
        self.low_stock_label.setVisible(False)
        layout.addWidget(self.low_stock_label)
        
        return panel
    
    def create_material_details_panel(self):
        """Create material details panel"""
        panel = QFrame()
        panel.setObjectName("detailsPanel")
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Material Details")
        title_label.setObjectName("panelTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Action buttons
        if self.auth_manager.has_permission('inventory_all') or self.auth_manager.has_permission('all'):
            self.edit_button = QPushButton("Edit")
            self.edit_button.setObjectName("secondaryButton")
            self.edit_button.clicked.connect(self.edit_material)
            self.edit_button.setEnabled(False)
            header_layout.addWidget(self.edit_button)
            
            self.save_button = QPushButton("Save")
            self.save_button.setObjectName("primaryButton")
            self.save_button.clicked.connect(self.save_material)
            self.save_button.setVisible(False)
            header_layout.addWidget(self.save_button)
            
            self.cancel_button = QPushButton("Cancel")
            self.cancel_button.setObjectName("secondaryButton")
            self.cancel_button.clicked.connect(self.cancel_edit)
            self.cancel_button.setVisible(False)
            header_layout.addWidget(self.cancel_button)
        
        layout.addLayout(header_layout)
        
        # Material form
        form_group = QGroupBox("Material Information")
        form_layout = QGridLayout(form_group)
        form_layout.setSpacing(10)
        
        # Material Code
        form_layout.addWidget(QLabel("Material Code:"), 0, 0)
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        form_layout.addWidget(self.code_input, 0, 1)
        
        # Material Name
        form_layout.addWidget(QLabel("Material Name:"), 1, 0)
        self.name_input = QLineEdit()
        self.name_input.setReadOnly(True)
        form_layout.addWidget(self.name_input, 1, 1)
        
        # Material Type
        form_layout.addWidget(QLabel("Type:"), 2, 0)
        self.type_combo = QComboBox()
        self.type_combo.addItems(["RAW_INGREDIENT", "FLAVOR", "CHEMICAL", "PACKAGING"])
        self.type_combo.setEnabled(False)
        form_layout.addWidget(self.type_combo, 2, 1)
        
        # Unit of Measure
        form_layout.addWidget(QLabel("Unit of Measure:"), 3, 0)
        self.uom_input = QLineEdit()
        self.uom_input.setReadOnly(True)
        form_layout.addWidget(self.uom_input, 3, 1)
        
        # Current Stock
        form_layout.addWidget(QLabel("Current Stock:"), 4, 0)
        self.stock_input = QDoubleSpinBox()
        self.stock_input.setDecimals(3)
        self.stock_input.setMaximum(999999.999)
        self.stock_input.setReadOnly(True)
        form_layout.addWidget(self.stock_input, 4, 1)
        
        # Minimum Threshold
        form_layout.addWidget(QLabel("Minimum Threshold:"), 5, 0)
        self.threshold_input = QDoubleSpinBox()
        self.threshold_input.setDecimals(3)
        self.threshold_input.setMaximum(999999.999)
        self.threshold_input.setReadOnly(True)
        form_layout.addWidget(self.threshold_input, 5, 1)
        
        # Unit Cost
        form_layout.addWidget(QLabel("Unit Cost:"), 6, 0)
        self.cost_input = QDoubleSpinBox()
        self.cost_input.setDecimals(2)
        self.cost_input.setMaximum(999999.99)
        self.cost_input.setPrefix("$")
        self.cost_input.setReadOnly(True)
        form_layout.addWidget(self.cost_input, 6, 1)
        
        # Supplier
        form_layout.addWidget(QLabel("Supplier:"), 7, 0)
        self.supplier_input = QLineEdit()
        self.supplier_input.setReadOnly(True)
        form_layout.addWidget(self.supplier_input, 7, 1)
        
        # Storage Location
        form_layout.addWidget(QLabel("Storage Location:"), 8, 0)
        self.location_input = QLineEdit()
        self.location_input.setReadOnly(True)
        form_layout.addWidget(self.location_input, 8, 1)
        
        # Expiry Date
        form_layout.addWidget(QLabel("Expiry Date:"), 9, 0)
        self.expiry_input = QDateEdit()
        self.expiry_input.setCalendarPopup(True)
        self.expiry_input.setReadOnly(True)
        form_layout.addWidget(self.expiry_input, 9, 1)
        
        layout.addWidget(form_group)
        
        # Status information
        status_group = QGroupBox("Status Information")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("No material selected")
        self.status_label.setObjectName("statusInfo")
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        
        return panel
    
    def get_stylesheet(self):
        """Return the stylesheet for the inventory management interface"""
        return f"""
            QWidget {{
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }}
            
            #listPanel, #detailsPanel {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
            }}
            
            #panelTitle {{
                font-size: 16px;
                font-weight: bold;
                color: {THEME_COLOR};
                margin-bottom: 10px;
            }}
            
            #primaryButton {{
                background-color: {THEME_COLOR};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            
            #primaryButton:hover {{
                background-color: #1e5f7a;
            }}
            
            #primaryButton:disabled {{
                background-color: #ccc;
                color: #666;
            }}
            
            #secondaryButton {{
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }}
            
            #secondaryButton:hover {{
                background-color: #545b62;
            }}
            
            #secondaryButton:disabled {{
                background-color: #ccc;
                color: #666;
            }}
            
            QTableWidget {{
                border: 1px solid #ddd;
                border-radius: 4px;
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }}
            
            QTableWidget::item:selected {{
                background-color: {THEME_COLOR};
                color: white;
            }}
            
            QHeaderView::section {{
                background-color: #e9ecef;
                padding: 8px;
                border: none;
                border-right: 1px solid #dee2e6;
                font-weight: bold;
            }}
            
            QLineEdit, QComboBox, QDoubleSpinBox, QDateEdit {{
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }}
            
            QLineEdit:focus, QComboBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
                border-color: {THEME_COLOR};
            }}
            
            QLineEdit:read-only, QDoubleSpinBox:read-only, QDateEdit:read-only {{
                background-color: #f8f9fa;
                color: #666;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 1px solid #ddd;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            #statusInfo {{
                font-size: 12px;
                color: #666;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }}
            
            #warningLabel {{
                color: {ERROR_COLOR};
                font-weight: bold;
                padding: 8px;
                background-color: #ffe6e6;
                border: 1px solid {ERROR_COLOR};
                border-radius: 4px;
            }}
        """
    
    def load_materials(self):
        """Load materials from database"""
        try:
            materials = self.db_manager.get_all_materials()
            self.populate_materials_table(materials)
            
            # Check for low stock items
            low_stock_materials = self.db_manager.get_low_stock_materials()
            if low_stock_materials:
                self.low_stock_label.setText(f"⚠️ {len(low_stock_materials)} items are below minimum threshold")
                self.low_stock_label.setVisible(True)
            else:
                self.low_stock_label.setVisible(False)
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load materials: {str(e)}")
    
    def populate_materials_table(self, materials):
        """Populate the materials table"""
        self.materials_table.setRowCount(len(materials))
        
        for row, material in enumerate(materials):
            # Material Code
            self.materials_table.setItem(row, 0, QTableWidgetItem(material.material_code))
            
            # Material Name
            self.materials_table.setItem(row, 1, QTableWidgetItem(material.material_name))
            
            # Type
            self.materials_table.setItem(row, 2, QTableWidgetItem(material.material_type))
            
            # Current Stock
            stock_item = QTableWidgetItem(f"{material.current_stock:.3f}")
            stock_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.materials_table.setItem(row, 3, stock_item)
            
            # Minimum Threshold
            threshold_item = QTableWidgetItem(f"{material.minimum_threshold:.3f}")
            threshold_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.materials_table.setItem(row, 4, threshold_item)
            
            # Unit Cost
            cost_item = QTableWidgetItem(f"${material.unit_cost:.2f}")
            cost_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.materials_table.setItem(row, 5, cost_item)
            
            # Status
            status = "Low Stock" if material.current_stock <= material.minimum_threshold else "OK"
            status_item = QTableWidgetItem(status)
            if status == "Low Stock":
                status_item.setBackground(QColor("#ffe6e6"))
                status_item.setForeground(QColor(ERROR_COLOR))
            else:
                status_item.setBackground(QColor("#e6ffe6"))
                status_item.setForeground(QColor(SUCCESS_COLOR))
            self.materials_table.setItem(row, 6, status_item)
            
            # Store material object in first column
            self.materials_table.item(row, 0).setData(Qt.UserRole, material)
    
    def filter_materials(self):
        """Filter materials based on search criteria"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter.currentText()
        
        for row in range(self.materials_table.rowCount()):
            show_row = True
            
            # Check search text
            if search_text:
                code = self.materials_table.item(row, 0).text().lower()
                name = self.materials_table.item(row, 1).text().lower()
                if search_text not in code and search_text not in name:
                    show_row = False
            
            # Check type filter
            if type_filter != "All Types":
                material_type = self.materials_table.item(row, 2).text()
                if material_type != type_filter:
                    show_row = False
            
            self.materials_table.setRowHidden(row, not show_row)
    
    def on_material_selected(self):
        """Handle material selection"""
        current_row = self.materials_table.currentRow()
        if current_row >= 0:
            material = self.materials_table.item(current_row, 0).data(Qt.UserRole)
            self.display_material_details(material)
            if hasattr(self, 'edit_button'):
                self.edit_button.setEnabled(True)
    
    def display_material_details(self, material):
        """Display material details in the form"""
        self.current_material = material
        
        self.code_input.setText(material.material_code)
        self.name_input.setText(material.material_name)
        self.type_combo.setCurrentText(material.material_type)
        self.uom_input.setText(material.unit_of_measure)
        self.stock_input.setValue(float(material.current_stock))
        self.threshold_input.setValue(float(material.minimum_threshold))
        self.cost_input.setValue(float(material.unit_cost))
        self.supplier_input.setText(material.supplier or "")
        self.location_input.setText(material.storage_location or "")
        
        if material.expiry_date:
            self.expiry_input.setDate(QDate.fromString(material.expiry_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        else:
            self.expiry_input.setDate(QDate.currentDate())
        
        # Update status
        status = "Low Stock" if material.current_stock <= material.minimum_threshold else "In Stock"
        self.status_label.setText(f"Status: {status} | Created: {material.created_date.strftime('%Y-%m-%d') if material.created_date else 'N/A'}")
    
    def add_new_material(self):
        """Add new material"""
        QMessageBox.information(self, "Add Material", "Add new material feature will be implemented in the next phase.")
    
    def edit_material(self):
        """Enable editing of current material"""
        if not self.current_material:
            return
        
        # Enable form fields
        self.name_input.setReadOnly(False)
        self.type_combo.setEnabled(True)
        self.uom_input.setReadOnly(False)
        self.stock_input.setReadOnly(False)
        self.threshold_input.setReadOnly(False)
        self.cost_input.setReadOnly(False)
        self.supplier_input.setReadOnly(False)
        self.location_input.setReadOnly(False)
        self.expiry_input.setReadOnly(False)
        
        # Show/hide buttons
        self.edit_button.setVisible(False)
        self.save_button.setVisible(True)
        self.cancel_button.setVisible(True)
    
    def save_material(self):
        """Save material changes"""
        QMessageBox.information(self, "Save Material", "Save material feature will be implemented in the next phase.")
        self.cancel_edit()
    
    def cancel_edit(self):
        """Cancel editing"""
        if self.current_material:
            self.display_material_details(self.current_material)
        
        # Disable form fields
        self.name_input.setReadOnly(True)
        self.type_combo.setEnabled(False)
        self.uom_input.setReadOnly(True)
        self.stock_input.setReadOnly(True)
        self.threshold_input.setReadOnly(True)
        self.cost_input.setReadOnly(True)
        self.supplier_input.setReadOnly(True)
        self.location_input.setReadOnly(True)
        self.expiry_input.setReadOnly(True)
        
        # Show/hide buttons
        self.edit_button.setVisible(True)
        self.save_button.setVisible(False)
        self.cancel_button.setVisible(False)
