"""
Perfect Login Window for SFF Production System
Balanced fonts, clear labels, and readable design
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class PerfectLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize perfect login interface"""
        self.setWindowTitle("SFF Production & Quality Management System")
        self.setFixedSize(500, 450)
        
        # Apply balanced design with clear fonts
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QWidget {
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #mainContainer {
                background-color: white;
                border: 2px solid #228B22;
                border-radius: 8px;
            }
            #headerSection {
                background-color: #228B22;
                border-radius: 8px 8px 0px 0px;
                padding: 25px;
            }
            #title {
                font-size: 22px;
                font-weight: bold;
                color: white;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #subtitle {
                font-size: 14px;
                color: #e8f5e8;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                margin-top: 8px;
            }
            #formSection {
                padding: 35px;
                background-color: white;
            }
            #fieldLabel {
                font-size: 16px;
                color: #228B22;
                font-weight: bold;
                margin-bottom: 10px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                background-color: white;
                padding: 5px 0px;
            }
            QLineEdit {
                padding: 15px 18px;
                border: 2px solid #90EE90;
                border-radius: 6px;
                font-size: 16px;
                background-color: white;
                margin-bottom: 20px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                color: #333;
                font-weight: normal;
            }
            QLineEdit:focus {
                border-color: #228B22;
                background-color: #f8fff8;
                outline: none;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: italic;
                font-weight: normal;
            }
            #loginButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 18px 30px;
                border-radius: 8px;
                font-size: 18px;
                font-weight: bold;
                margin-top: 15px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                min-height: 25px;
            }
            #loginButton:hover {
                background-color: #32CD32;
            }
            #loginButton:pressed {
                background-color: #006400;
            }
            #statusLabel {
                color: #dc3545;
                font-weight: bold;
                margin-top: 15px;
                font-size: 14px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #infoLabel {
                color: #666;
                font-size: 13px;
                margin-top: 20px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: normal;
            }
        """)
        
        # Center the window
        self.center_window()
        
        # Create main container
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(0)
        
        # Create main container frame
        container = QFrame()
        container.setObjectName("mainContainer")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # Header section
        header_section = QFrame()
        header_section.setObjectName("headerSection")
        header_layout = QVBoxLayout(header_section)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setAlignment(Qt.AlignCenter)
        
        title = QLabel("SFF Production System")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        subtitle = QLabel("Production & Quality Management")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle)
        
        container_layout.addWidget(header_section)
        
        # Form section
        form_section = QFrame()
        form_section.setObjectName("formSection")
        form_layout = QVBoxLayout(form_section)
        form_layout.setSpacing(8)
        
        # Username field
        username_label = QLabel("Username")
        username_label.setObjectName("fieldLabel")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your username")
        self.username_input.setText("admin")
        form_layout.addWidget(self.username_input)
        
        # Password field
        password_label = QLabel("Password")
        password_label.setObjectName("fieldLabel")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        form_layout.addWidget(self.password_input)
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login_clicked)
        form_layout.addWidget(self.login_button)
        
        # Status message
        self.status_label = QLabel("")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(self.status_label)
        
        # Info label
        info_label = QLabel("Default credentials: admin / admin123")
        info_label.setObjectName("infoLabel")
        info_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(info_label)
        
        container_layout.addWidget(form_section)
        main_layout.addWidget(container)
        
        # Set focus and connect Enter key
        self.username_input.setFocus()
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login_clicked(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # Clear previous status
        self.status_label.setText("")
        
        # Validate input
        if not username:
            self.show_status("Please enter your username")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("Please enter your password")
            self.password_input.setFocus()
            return
        
        # Validate credentials
        if username == "admin" and password == "admin123":
            self.show_status("Login successful!", "success")
            
            # Show success message
            QMessageBox.information(
                self, 
                "Login Successful", 
                f"Welcome to SFF Production System!\n\n"
                f"User: {username}\n"
                f"Role: System Administrator\n\n"
                f"Opening main dashboard..."
            )
            
            # Open dashboard
            self.open_dashboard()
        else:
            self.show_status("Invalid username or password")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, status_type="error"):
        """Show status message"""
        self.status_label.setText(message)
        
        if status_type == "success":
            self.status_label.setStyleSheet("color: #228B22; font-weight: bold; font-size: 14px;")
        else:
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold; font-size: 14px;")
    
    def open_dashboard(self):
        """Open main dashboard"""
        try:
            from gui.perfect_dashboard import PerfectDashboard
            self.dashboard = PerfectDashboard()
            self.dashboard.show()
            self.close()
        except Exception as e:
            QMessageBox.information(
                self,
                "Login Successful",
                f"Login successful!\n\n"
                f"System is ready for use.\n"
                f"Dashboard will open shortly.\n\n"
                f"Note: {str(e)}"
            )

def main():
    """Run the perfect login window"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")
    
    window = PerfectLoginWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
