// qlabel.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLabel : public QFrame
{
%TypeHeaderCode
#include <qlabel.h>
%End

public:
    QLabel(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QLabel(const QString &text, QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QLabel();
    QString text() const;
    const QPixmap *pixmap() const;
    const QPicture *picture() const;
    QMovie *movie() const;
    Qt::TextFormat textFormat() const;
    void setTextFormat(Qt::TextFormat);
    Qt::Alignment alignment() const;
    void setAlignment(Qt::Alignment);
    void setWordWrap(bool on);
    bool wordWrap() const;
    int indent() const;
    void setIndent(int);
    int margin() const;
    void setMargin(int);
    bool hasScaledContents() const;
    void setScaledContents(bool);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setBuddy(QWidget * /KeepReference/);
    QWidget *buddy() const;
    virtual int heightForWidth(int) const;
    bool openExternalLinks() const;
    void setTextInteractionFlags(Qt::TextInteractionFlags flags);
    Qt::TextInteractionFlags textInteractionFlags() const;
    void setOpenExternalLinks(bool open);

public slots:
    void clear();
    void setMovie(QMovie *movie /KeepReference/);
    void setNum(double /Constrained/);
    void setNum(int);
    void setPicture(const QPicture &);
    void setPixmap(const QPixmap &);
    void setText(const QString &);

signals:
    void linkActivated(const QString &link);
    void linkHovered(const QString &link);

protected:
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
    virtual void changeEvent(QEvent *);
    virtual void keyPressEvent(QKeyEvent *ev);
    virtual void mousePressEvent(QMouseEvent *ev);
    virtual void mouseMoveEvent(QMouseEvent *ev);
    virtual void mouseReleaseEvent(QMouseEvent *ev);
    virtual void contextMenuEvent(QContextMenuEvent *ev);
    virtual void focusInEvent(QFocusEvent *ev);
    virtual void focusOutEvent(QFocusEvent *ev);
    virtual bool focusNextPrevChild(bool next);

public:
    void setSelection(int, int);
    bool hasSelectedText() const;
    QString selectedText() const;
    int selectionStart() const;
};
