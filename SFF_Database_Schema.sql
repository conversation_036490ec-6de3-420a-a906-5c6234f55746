-- نظام إدارة الإنتاج والجودة SFF
-- ملف إنشاء قاعدة البيانات SQL
-- يمكن تشغيله في Microsoft Access

-- 1. جدول المستخدمين
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username VARCHAR(50) NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    Email VARCHAR(100),
    FullName VARCHAR(100) NOT NULL,
    Role VARCHAR(20) NOT NULL,
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME,
    FailedLoginAttempts INTEGER DEFAULT 0,
    LockedUntil DATETIME
);

-- 2. جدول المواد الخام والمكونات
CREATE TABLE Materials (
    MaterialID AUTOINCREMENT PRIMARY KEY,
    MaterialCode VARCHAR(20) NOT NULL,
    MaterialName VARCHAR(100) NOT NULL,
    MaterialType VARCHAR(20) NOT NULL,
    UnitOfMeasure VARCHAR(10) NOT NULL,
    CurrentStock DECIMAL(10,3) DEFAULT 0,
    MinimumThreshold DECIMAL(10,3) DEFAULT 0,
    UnitCost CURRENCY DEFAULT 0,
    Supplier VARCHAR(100),
    StorageLocation VARCHAR(50),
    ExpiryDate DATETIME,
    CreatedDate DATETIME DEFAULT Now(),
    IsActive YESNO DEFAULT True
);

-- 3. جدول الوصفات
CREATE TABLE Recipes (
    RecipeID AUTOINCREMENT PRIMARY KEY,
    RecipeCode VARCHAR(20) NOT NULL,
    RecipeName VARCHAR(100) NOT NULL,
    Version VARCHAR(10) DEFAULT '1.0',
    Description MEMO,
    BatchSize DECIMAL(10,3) NOT NULL,
    UnitOfMeasure VARCHAR(10) NOT NULL,
    TotalCost CURRENCY DEFAULT 0,
    IsApproved YESNO DEFAULT False,
    CreatedBy INTEGER NOT NULL,
    CreatedDate DATETIME DEFAULT Now(),
    ApprovedBy INTEGER,
    ApprovedDate DATETIME,
    IsActive YESNO DEFAULT True
);

-- 4. جدول مكونات الوصفات
CREATE TABLE RecipeIngredients (
    RecipeIngredientID AUTOINCREMENT PRIMARY KEY,
    RecipeID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DECIMAL(10,3) NOT NULL,
    UnitOfMeasure VARCHAR(10) NOT NULL,
    Percentage DECIMAL(5,2),
    Notes MEMO
);

-- 5. جدول دفعات الإنتاج
CREATE TABLE ProductionBatches (
    BatchID AUTOINCREMENT PRIMARY KEY,
    BatchNumber VARCHAR(30) NOT NULL,
    RecipeID INTEGER NOT NULL,
    BatchType VARCHAR(20) DEFAULT 'PRODUCTION',
    PlannedQuantity DECIMAL(10,3) NOT NULL,
    ActualQuantity DECIMAL(10,3) DEFAULT 0,
    Status VARCHAR(20) DEFAULT 'PLANNED',
    OperatorID INTEGER NOT NULL,
    StartDate DATETIME,
    EndDate DATETIME,
    TotalCost CURRENCY DEFAULT 0,
    Notes MEMO,
    CreatedDate DATETIME DEFAULT Now()
);

-- 6. جدول استخدام المواد في الدفعات
CREATE TABLE BatchIngredientUsage (
    UsageID AUTOINCREMENT PRIMARY KEY,
    BatchID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    PlannedQuantity DECIMAL(10,3) NOT NULL,
    ActualQuantity DECIMAL(10,3) DEFAULT 0,
    UnitCost CURRENCY NOT NULL,
    TotalCost CURRENCY DEFAULT 0
);

-- 7. جدول اختبارات الجودة
CREATE TABLE QualityTests (
    TestID AUTOINCREMENT PRIMARY KEY,
    BatchID INTEGER NOT NULL,
    TestType VARCHAR(50) NOT NULL,
    TestParameter VARCHAR(100) NOT NULL,
    Specification VARCHAR(100),
    Result VARCHAR(100),
    Status VARCHAR(20) DEFAULT 'PENDING',
    TestedBy INTEGER NOT NULL,
    TestDate DATETIME DEFAULT Now(),
    Notes MEMO
);

-- 8. جدول وثائق الجودة
CREATE TABLE QualityDocuments (
    DocumentID AUTOINCREMENT PRIMARY KEY,
    BatchID INTEGER NOT NULL,
    DocumentType VARCHAR(20) NOT NULL,
    DocumentContent MEMO,
    GeneratedBy INTEGER NOT NULL,
    GeneratedDate DATETIME DEFAULT Now(),
    IsApproved YESNO DEFAULT False,
    ApprovedBy INTEGER,
    ApprovedDate DATETIME
);

-- 9. جدول حركات المخزون
CREATE TABLE InventoryMovements (
    MovementID AUTOINCREMENT PRIMARY KEY,
    MaterialID INTEGER NOT NULL,
    MovementType VARCHAR(20) NOT NULL,
    Quantity DECIMAL(10,3) NOT NULL,
    ReferenceID INTEGER,
    ReferenceType VARCHAR(20),
    PerformedBy INTEGER NOT NULL,
    MovementDate DATETIME DEFAULT Now(),
    Notes MEMO
);

-- 10. جدول سجل المراجعة
CREATE TABLE AuditLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID INTEGER NOT NULL,
    Action VARCHAR(50) NOT NULL,
    TableName VARCHAR(50) NOT NULL,
    RecordID INTEGER,
    OldValues MEMO,
    NewValues MEMO,
    Timestamp DATETIME DEFAULT Now(),
    IPAddress VARCHAR(15)
);

-- إدراج المستخدم الافتراضي (المدير)
-- ملاحظة: كلمة المرور مشفرة باستخدام bcrypt
INSERT INTO Users (Username, PasswordHash, Email, FullName, Role, IsActive)
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXfs2Stk5v9W', '<EMAIL>', 'مدير النظام', 'ADMIN', True);

-- إدراج بيانات تجريبية للمواد
INSERT INTO Materials (MaterialCode, MaterialName, MaterialType, UnitOfMeasure, CurrentStock, MinimumThreshold, UnitCost, Supplier, StorageLocation, IsActive)
VALUES 
('RAW001', 'دقيق القمح', 'RAW_INGREDIENT', 'كيلو', 1000, 50, 2.5, 'مورد المواد الخام', 'مخزن A', True),
('RAW002', 'سكر أبيض', 'RAW_INGREDIENT', 'كيلو', 500, 25, 3.0, 'مورد المواد الخام', 'مخزن A', True),
('FLV001', 'نكهة الفانيليا', 'FLAVOR', 'لتر', 20, 5, 15.0, 'مورد النكهات', 'مخزن B', True),
('CHM001', 'بيكنج بودر', 'CHEMICAL', 'كيلو', 100, 10, 8.0, 'مورد الكيماويات', 'مخزن C', True),
('PKG001', 'أكياس تعبئة', 'PACKAGING', 'قطعة', 5000, 500, 0.1, 'مورد التعبئة', 'مخزن D', True);

-- إدراج وصفة تجريبية
INSERT INTO Recipes (RecipeCode, RecipeName, Description, BatchSize, UnitOfMeasure, TotalCost, IsApproved, CreatedBy, IsActive)
VALUES ('RCP001', 'كعكة الفانيليا الأساسية', 'وصفة أساسية لكعكة الفانيليا', 10.0, 'كيلو', 0, True, 1, True);

-- إدراج مكونات الوصفة
INSERT INTO RecipeIngredients (RecipeID, MaterialID, Quantity, UnitOfMeasure, Percentage, Notes)
VALUES 
(1, 1, 5.0, 'كيلو', 50.0, 'المكون الأساسي'),
(1, 2, 2.0, 'كيلو', 20.0, 'للتحلية'),
(1, 3, 0.1, 'لتر', 1.0, 'للنكهة'),
(1, 4, 0.2, 'كيلو', 2.0, 'للانتفاخ');
