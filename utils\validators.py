"""
SFF Production & Quality Management System
Data Validation Utilities
"""

import re
from decimal import Decimal, InvalidOperation
from datetime import datetime
from typing import Dict, Any, List, Optional

class ValidationResult:
    """Class to hold validation results"""
    def __init__(self, is_valid: bool = True, errors: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
    
    def add_error(self, error: str):
        """Add an error to the validation result"""
        self.is_valid = False
        self.errors.append(error)

class DataValidator:
    """Data validation utility class"""
    
    @staticmethod
    def validate_username(username: str) -> ValidationResult:
        """Validate username format"""
        result = ValidationResult()
        
        if not username:
            result.add_error("Username is required")
            return result
        
        if len(username) < 3:
            result.add_error("Username must be at least 3 characters long")
        
        if len(username) > 50:
            result.add_error("Username must be less than 50 characters")
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            result.add_error("Username can only contain letters, numbers, and underscores")
        
        return result
    
    @staticmethod
    def validate_email(email: str) -> ValidationResult:
        """Validate email format"""
        result = ValidationResult()
        
        if not email:
            result.add_error("Email is required")
            return result
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            result.add_error("Invalid email format")
        
        return result
    
    @staticmethod
    def validate_material_code(code: str) -> ValidationResult:
        """Validate material code format"""
        result = ValidationResult()
        
        if not code:
            result.add_error("Material code is required")
            return result
        
        if len(code) < 2:
            result.add_error("Material code must be at least 2 characters long")
        
        if len(code) > 20:
            result.add_error("Material code must be less than 20 characters")
        
        if not re.match(r'^[A-Z0-9_-]+$', code.upper()):
            result.add_error("Material code can only contain letters, numbers, hyphens, and underscores")
        
        return result
    
    @staticmethod
    def validate_recipe_code(code: str) -> ValidationResult:
        """Validate recipe code format"""
        result = ValidationResult()
        
        if not code:
            result.add_error("Recipe code is required")
            return result
        
        if len(code) < 2:
            result.add_error("Recipe code must be at least 2 characters long")
        
        if len(code) > 20:
            result.add_error("Recipe code must be less than 20 characters")
        
        if not re.match(r'^[A-Z0-9_-]+$', code.upper()):
            result.add_error("Recipe code can only contain letters, numbers, hyphens, and underscores")
        
        return result
    
    @staticmethod
    def validate_batch_number(batch_number: str) -> ValidationResult:
        """Validate batch number format"""
        result = ValidationResult()
        
        if not batch_number:
            result.add_error("Batch number is required")
            return result
        
        if len(batch_number) < 3:
            result.add_error("Batch number must be at least 3 characters long")
        
        if len(batch_number) > 30:
            result.add_error("Batch number must be less than 30 characters")
        
        return result
    
    @staticmethod
    def validate_decimal(value: str, field_name: str, min_value: float = None, max_value: float = None) -> ValidationResult:
        """Validate decimal number"""
        result = ValidationResult()
        
        if not value:
            result.add_error(f"{field_name} is required")
            return result
        
        try:
            decimal_value = Decimal(value)
            
            if min_value is not None and decimal_value < Decimal(str(min_value)):
                result.add_error(f"{field_name} must be at least {min_value}")
            
            if max_value is not None and decimal_value > Decimal(str(max_value)):
                result.add_error(f"{field_name} must be at most {max_value}")
            
        except InvalidOperation:
            result.add_error(f"{field_name} must be a valid number")
        
        return result
    
    @staticmethod
    def validate_positive_decimal(value: str, field_name: str) -> ValidationResult:
        """Validate positive decimal number"""
        return DataValidator.validate_decimal(value, field_name, min_value=0)
    
    @staticmethod
    def validate_percentage(value: str, field_name: str) -> ValidationResult:
        """Validate percentage (0-100)"""
        return DataValidator.validate_decimal(value, field_name, min_value=0, max_value=100)
    
    @staticmethod
    def validate_required_text(value: str, field_name: str, min_length: int = 1, max_length: int = 255) -> ValidationResult:
        """Validate required text field"""
        result = ValidationResult()
        
        if not value or not value.strip():
            result.add_error(f"{field_name} is required")
            return result
        
        value = value.strip()
        
        if len(value) < min_length:
            result.add_error(f"{field_name} must be at least {min_length} characters long")
        
        if len(value) > max_length:
            result.add_error(f"{field_name} must be less than {max_length} characters")
        
        return result
    
    @staticmethod
    def validate_date(date_str: str, field_name: str, date_format: str = "%Y-%m-%d") -> ValidationResult:
        """Validate date format"""
        result = ValidationResult()
        
        if not date_str:
            result.add_error(f"{field_name} is required")
            return result
        
        try:
            datetime.strptime(date_str, date_format)
        except ValueError:
            result.add_error(f"{field_name} must be in format {date_format}")
        
        return result
    
    @staticmethod
    def validate_choice(value: str, field_name: str, valid_choices: List[str]) -> ValidationResult:
        """Validate choice from list of valid options"""
        result = ValidationResult()
        
        if not value:
            result.add_error(f"{field_name} is required")
            return result
        
        if value not in valid_choices:
            result.add_error(f"{field_name} must be one of: {', '.join(valid_choices)}")
        
        return result
    
    @staticmethod
    def validate_material_data(data: Dict[str, Any]) -> ValidationResult:
        """Validate complete material data"""
        result = ValidationResult()
        
        # Validate material code
        code_result = DataValidator.validate_material_code(data.get('material_code', ''))
        if not code_result.is_valid:
            result.errors.extend(code_result.errors)
            result.is_valid = False
        
        # Validate material name
        name_result = DataValidator.validate_required_text(data.get('material_name', ''), "Material name", 2, 100)
        if not name_result.is_valid:
            result.errors.extend(name_result.errors)
            result.is_valid = False
        
        # Validate material type
        valid_types = ['RAW_INGREDIENT', 'FLAVOR', 'CHEMICAL', 'PACKAGING']
        type_result = DataValidator.validate_choice(data.get('material_type', ''), "Material type", valid_types)
        if not type_result.is_valid:
            result.errors.extend(type_result.errors)
            result.is_valid = False
        
        # Validate unit of measure
        uom_result = DataValidator.validate_required_text(data.get('unit_of_measure', ''), "Unit of measure", 1, 10)
        if not uom_result.is_valid:
            result.errors.extend(uom_result.errors)
            result.is_valid = False
        
        # Validate current stock
        stock_result = DataValidator.validate_positive_decimal(str(data.get('current_stock', '')), "Current stock")
        if not stock_result.is_valid:
            result.errors.extend(stock_result.errors)
            result.is_valid = False
        
        # Validate minimum threshold
        threshold_result = DataValidator.validate_positive_decimal(str(data.get('minimum_threshold', '')), "Minimum threshold")
        if not threshold_result.is_valid:
            result.errors.extend(threshold_result.errors)
            result.is_valid = False
        
        # Validate unit cost
        cost_result = DataValidator.validate_positive_decimal(str(data.get('unit_cost', '')), "Unit cost")
        if not cost_result.is_valid:
            result.errors.extend(cost_result.errors)
            result.is_valid = False
        
        return result
    
    @staticmethod
    def validate_recipe_data(data: Dict[str, Any]) -> ValidationResult:
        """Validate complete recipe data"""
        result = ValidationResult()
        
        # Validate recipe code
        code_result = DataValidator.validate_recipe_code(data.get('recipe_code', ''))
        if not code_result.is_valid:
            result.errors.extend(code_result.errors)
            result.is_valid = False
        
        # Validate recipe name
        name_result = DataValidator.validate_required_text(data.get('recipe_name', ''), "Recipe name", 2, 100)
        if not name_result.is_valid:
            result.errors.extend(name_result.errors)
            result.is_valid = False
        
        # Validate batch size
        size_result = DataValidator.validate_positive_decimal(str(data.get('batch_size', '')), "Batch size")
        if not size_result.is_valid:
            result.errors.extend(size_result.errors)
            result.is_valid = False
        
        # Validate unit of measure
        uom_result = DataValidator.validate_required_text(data.get('unit_of_measure', ''), "Unit of measure", 1, 10)
        if not uom_result.is_valid:
            result.errors.extend(uom_result.errors)
            result.is_valid = False
        
        return result
    
    @staticmethod
    def validate_user_data(data: Dict[str, Any]) -> ValidationResult:
        """Validate complete user data"""
        result = ValidationResult()
        
        # Validate username
        username_result = DataValidator.validate_username(data.get('username', ''))
        if not username_result.is_valid:
            result.errors.extend(username_result.errors)
            result.is_valid = False
        
        # Validate email
        email_result = DataValidator.validate_email(data.get('email', ''))
        if not email_result.is_valid:
            result.errors.extend(email_result.errors)
            result.is_valid = False
        
        # Validate full name
        name_result = DataValidator.validate_required_text(data.get('full_name', ''), "Full name", 2, 100)
        if not name_result.is_valid:
            result.errors.extend(name_result.errors)
            result.is_valid = False
        
        # Validate role
        valid_roles = ['ADMIN', 'PRODUCTION', 'QUALITY', 'WAREHOUSE', 'VIEWER']
        role_result = DataValidator.validate_choice(data.get('role', ''), "Role", valid_roles)
        if not role_result.is_valid:
            result.errors.extend(role_result.errors)
            result.is_valid = False
        
        return result
