"""
إنشاء قاعدة بيانات Microsoft Access لنظام إدارة الإنتاج والجودة SFF
"""

import pyodbc
import os
from datetime import datetime

def create_sff_database():
    """إنشاء قاعدة بيانات SFF مع جميع الجداول المطلوبة"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.getcwd(), "SFF_Production_Database.accdb")
    
    # حذف قاعدة البيانات إذا كانت موجودة
    if os.path.exists(db_path):
        os.remove(db_path)
        print("تم حذف قاعدة البيانات القديمة")
    
    # إنشاء قاعدة بيانات جديدة
    try:
        # استخدام ADOX لإنشاء قاعدة بيانات جديدة
        import win32com.client
        
        catalog = win32com.client.Dispatch("ADOX.Catalog")
        catalog.Create(f"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={db_path}")
        catalog = None
        
        print(f"تم إنشاء قاعدة البيانات: {db_path}")
        
    except ImportError:
        print("تحذير: لا يمكن استخدام win32com، سيتم المحاولة بطريقة أخرى")
        # إنشاء قاعدة بيانات فارغة باستخدام pyodbc
        conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};'
        try:
            conn = pyodbc.connect(conn_str)
            conn.close()
        except:
            print("خطأ: لا يمكن إنشاء قاعدة البيانات. تأكد من تثبيت Microsoft Access Database Engine")
            return False
    
    # الاتصال بقاعدة البيانات وإنشاء الجداول
    conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};'
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        print("بدء إنشاء الجداول...")
        
        # 1. جدول المستخدمين
        print("إنشاء جدول المستخدمين...")
        cursor.execute('''
            CREATE TABLE Users (
                UserID AUTOINCREMENT PRIMARY KEY,
                Username VARCHAR(50) NOT NULL,
                PasswordHash VARCHAR(255) NOT NULL,
                Email VARCHAR(100),
                FullName VARCHAR(100) NOT NULL,
                Role VARCHAR(20) NOT NULL,
                IsActive YESNO DEFAULT True,
                CreatedDate DATETIME DEFAULT Now(),
                LastLogin DATETIME,
                FailedLoginAttempts INTEGER DEFAULT 0,
                LockedUntil DATETIME
            )
        ''')
        
        # 2. جدول المواد الخام
        print("إنشاء جدول المواد الخام...")
        cursor.execute('''
            CREATE TABLE Materials (
                MaterialID AUTOINCREMENT PRIMARY KEY,
                MaterialCode VARCHAR(20) NOT NULL,
                MaterialName VARCHAR(100) NOT NULL,
                MaterialType VARCHAR(20) NOT NULL,
                UnitOfMeasure VARCHAR(10) NOT NULL,
                CurrentStock DECIMAL(10,3) DEFAULT 0,
                MinimumThreshold DECIMAL(10,3) DEFAULT 0,
                UnitCost CURRENCY DEFAULT 0,
                Supplier VARCHAR(100),
                StorageLocation VARCHAR(50),
                ExpiryDate DATETIME,
                CreatedDate DATETIME DEFAULT Now(),
                IsActive YESNO DEFAULT True
            )
        ''')
        
        # 3. جدول الوصفات
        print("إنشاء جدول الوصفات...")
        cursor.execute('''
            CREATE TABLE Recipes (
                RecipeID AUTOINCREMENT PRIMARY KEY,
                RecipeCode VARCHAR(20) NOT NULL,
                RecipeName VARCHAR(100) NOT NULL,
                Version VARCHAR(10) DEFAULT '1.0',
                Description MEMO,
                BatchSize DECIMAL(10,3) NOT NULL,
                UnitOfMeasure VARCHAR(10) NOT NULL,
                TotalCost CURRENCY DEFAULT 0,
                IsApproved YESNO DEFAULT False,
                CreatedBy INTEGER NOT NULL,
                CreatedDate DATETIME DEFAULT Now(),
                ApprovedBy INTEGER,
                ApprovedDate DATETIME,
                IsActive YESNO DEFAULT True
            )
        ''')
        
        # 4. جدول مكونات الوصفات
        print("إنشاء جدول مكونات الوصفات...")
        cursor.execute('''
            CREATE TABLE RecipeIngredients (
                RecipeIngredientID AUTOINCREMENT PRIMARY KEY,
                RecipeID INTEGER NOT NULL,
                MaterialID INTEGER NOT NULL,
                Quantity DECIMAL(10,3) NOT NULL,
                UnitOfMeasure VARCHAR(10) NOT NULL,
                Percentage DECIMAL(5,2),
                Notes MEMO
            )
        ''')
        
        # 5. جدول دفعات الإنتاج
        print("إنشاء جدول دفعات الإنتاج...")
        cursor.execute('''
            CREATE TABLE ProductionBatches (
                BatchID AUTOINCREMENT PRIMARY KEY,
                BatchNumber VARCHAR(30) NOT NULL,
                RecipeID INTEGER NOT NULL,
                BatchType VARCHAR(20) DEFAULT 'PRODUCTION',
                PlannedQuantity DECIMAL(10,3) NOT NULL,
                ActualQuantity DECIMAL(10,3) DEFAULT 0,
                Status VARCHAR(20) DEFAULT 'PLANNED',
                OperatorID INTEGER NOT NULL,
                StartDate DATETIME,
                EndDate DATETIME,
                TotalCost CURRENCY DEFAULT 0,
                Notes MEMO,
                CreatedDate DATETIME DEFAULT Now()
            )
        ''')
        
        # 6. جدول استخدام المواد في الدفعات
        print("إنشاء جدول استخدام المواد...")
        cursor.execute('''
            CREATE TABLE BatchIngredientUsage (
                UsageID AUTOINCREMENT PRIMARY KEY,
                BatchID INTEGER NOT NULL,
                MaterialID INTEGER NOT NULL,
                PlannedQuantity DECIMAL(10,3) NOT NULL,
                ActualQuantity DECIMAL(10,3) DEFAULT 0,
                UnitCost CURRENCY NOT NULL,
                TotalCost CURRENCY DEFAULT 0
            )
        ''')
        
        # 7. جدول اختبارات الجودة
        print("إنشاء جدول اختبارات الجودة...")
        cursor.execute('''
            CREATE TABLE QualityTests (
                TestID AUTOINCREMENT PRIMARY KEY,
                BatchID INTEGER NOT NULL,
                TestType VARCHAR(50) NOT NULL,
                TestParameter VARCHAR(100) NOT NULL,
                Specification VARCHAR(100),
                Result VARCHAR(100),
                Status VARCHAR(20) DEFAULT 'PENDING',
                TestedBy INTEGER NOT NULL,
                TestDate DATETIME DEFAULT Now(),
                Notes MEMO
            )
        ''')
        
        # 8. جدول وثائق الجودة
        print("إنشاء جدول وثائق الجودة...")
        cursor.execute('''
            CREATE TABLE QualityDocuments (
                DocumentID AUTOINCREMENT PRIMARY KEY,
                BatchID INTEGER NOT NULL,
                DocumentType VARCHAR(20) NOT NULL,
                DocumentContent MEMO,
                GeneratedBy INTEGER NOT NULL,
                GeneratedDate DATETIME DEFAULT Now(),
                IsApproved YESNO DEFAULT False,
                ApprovedBy INTEGER,
                ApprovedDate DATETIME
            )
        ''')
        
        # 9. جدول حركات المخزون
        print("إنشاء جدول حركات المخزون...")
        cursor.execute('''
            CREATE TABLE InventoryMovements (
                MovementID AUTOINCREMENT PRIMARY KEY,
                MaterialID INTEGER NOT NULL,
                MovementType VARCHAR(20) NOT NULL,
                Quantity DECIMAL(10,3) NOT NULL,
                ReferenceID INTEGER,
                ReferenceType VARCHAR(20),
                PerformedBy INTEGER NOT NULL,
                MovementDate DATETIME DEFAULT Now(),
                Notes MEMO
            )
        ''')
        
        # 10. جدول سجل المراجعة
        print("إنشاء جدول سجل المراجعة...")
        cursor.execute('''
            CREATE TABLE AuditLog (
                LogID AUTOINCREMENT PRIMARY KEY,
                UserID INTEGER NOT NULL,
                Action VARCHAR(50) NOT NULL,
                TableName VARCHAR(50) NOT NULL,
                RecordID INTEGER,
                OldValues MEMO,
                NewValues MEMO,
                Timestamp DATETIME DEFAULT Now(),
                IPAddress VARCHAR(15)
            )
        ''')
        
        conn.commit()
        print("تم إنشاء جميع الجداول بنجاح!")
        
        # إنشاء مستخدم المدير الافتراضي
        print("إنشاء مستخدم المدير الافتراضي...")
        import bcrypt
        
        password = "admin123"
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT INTO Users (Username, PasswordHash, Email, FullName, Role, IsActive)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('admin', password_hash, '<EMAIL>', 'مدير النظام', 'ADMIN', True))
        
        conn.commit()
        print("تم إنشاء مستخدم المدير: admin / admin123")
        
        # إضافة بعض البيانات التجريبية للمواد
        print("إضافة بيانات تجريبية للمواد...")
        
        sample_materials = [
            ('RAW001', 'دقيق القمح', 'RAW_INGREDIENT', 'كيلو', 1000, 50, 2.5, 'مورد المواد الخام', 'مخزن A', None),
            ('RAW002', 'سكر أبيض', 'RAW_INGREDIENT', 'كيلو', 500, 25, 3.0, 'مورد المواد الخام', 'مخزن A', None),
            ('FLV001', 'نكهة الفانيليا', 'FLAVOR', 'لتر', 20, 5, 15.0, 'مورد النكهات', 'مخزن B', None),
            ('CHM001', 'بيكنج بودر', 'CHEMICAL', 'كيلو', 100, 10, 8.0, 'مورد الكيماويات', 'مخزن C', None),
            ('PKG001', 'أكياس تعبئة', 'PACKAGING', 'قطعة', 5000, 500, 0.1, 'مورد التعبئة', 'مخزن D', None)
        ]
        
        for material in sample_materials:
            cursor.execute('''
                INSERT INTO Materials (MaterialCode, MaterialName, MaterialType, UnitOfMeasure, 
                                     CurrentStock, MinimumThreshold, UnitCost, Supplier, 
                                     StorageLocation, ExpiryDate, IsActive)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', material + (True,))
        
        conn.commit()
        print("تم إضافة البيانات التجريبية للمواد")
        
        # إضافة وصفة تجريبية
        print("إضافة وصفة تجريبية...")
        
        cursor.execute('''
            INSERT INTO Recipes (RecipeCode, RecipeName, Description, BatchSize, UnitOfMeasure, 
                               TotalCost, IsApproved, CreatedBy, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('RCP001', 'كعكة الفانيليا الأساسية', 'وصفة أساسية لكعكة الفانيليا', 
              10.0, 'كيلو', 0, True, 1, True))
        
        # الحصول على معرف الوصفة
        cursor.execute("SELECT @@IDENTITY")
        recipe_id = cursor.fetchone()[0]
        
        # إضافة مكونات الوصفة
        recipe_ingredients = [
            (recipe_id, 1, 5.0, 'كيلو', 50.0, 'المكون الأساسي'),  # دقيق
            (recipe_id, 2, 2.0, 'كيلو', 20.0, 'للتحلية'),        # سكر
            (recipe_id, 3, 0.1, 'لتر', 1.0, 'للنكهة'),          # فانيليا
            (recipe_id, 4, 0.2, 'كيلو', 2.0, 'للانتفاخ')        # بيكنج بودر
        ]
        
        for ingredient in recipe_ingredients:
            cursor.execute('''
                INSERT INTO RecipeIngredients (RecipeID, MaterialID, Quantity, UnitOfMeasure, 
                                             Percentage, Notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ingredient)
        
        conn.commit()
        print("تم إضافة الوصفة التجريبية")
        
        conn.close()
        
        print("\n" + "="*60)
        print("🎉 تم إنشاء قاعدة بيانات SFF بنجاح!")
        print("="*60)
        print(f"📁 مسار قاعدة البيانات: {db_path}")
        print("👤 بيانات تسجيل الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n📊 تم إنشاء الجداول التالية:")
        print("   ✅ جدول المستخدمين (Users)")
        print("   ✅ جدول المواد الخام (Materials)")
        print("   ✅ جدول الوصفات (Recipes)")
        print("   ✅ جدول مكونات الوصفات (RecipeIngredients)")
        print("   ✅ جدول دفعات الإنتاج (ProductionBatches)")
        print("   ✅ جدول استخدام المواد (BatchIngredientUsage)")
        print("   ✅ جدول اختبارات الجودة (QualityTests)")
        print("   ✅ جدول وثائق الجودة (QualityDocuments)")
        print("   ✅ جدول حركات المخزون (InventoryMovements)")
        print("   ✅ جدول سجل المراجعة (AuditLog)")
        print("\n📦 تم إضافة بيانات تجريبية:")
        print("   • 5 مواد خام مختلفة")
        print("   • وصفة كعكة الفانيليا مع مكوناتها")
        print("   • مستخدم المدير الافتراضي")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("نظام إدارة الإنتاج والجودة SFF")
    print("إنشاء قاعدة بيانات Microsoft Access")
    print("="*50)
    
    success = create_sff_database()
    
    if success:
        print("\n✅ تم إنشاء قاعدة البيانات بنجاح!")
        print("يمكنك الآن فتح قاعدة البيانات باستخدام Microsoft Access")
    else:
        print("\n❌ فشل في إنشاء قاعدة البيانات")
        print("تأكد من تثبيت Microsoft Access Database Engine")
