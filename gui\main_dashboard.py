"""
SFF Production & Quality Management System
Main Dashboard Window
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QMenuBar, QStatusBar, QAction,
    QMessageBox, QTabWidget, QScrollArea, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette
from auth.authentication import AuthenticationManager
from config.settings import (
    APP_NAME, WINDOW_WIDTH, WINDOW_HEIGHT, THEME_COLOR, 
    SECONDARY_COLOR, USER_ROLES, ROLE_PERMISSIONS
)

class DashboardCard(QFrame):
    """Custom widget for dashboard cards"""
    clicked = pyqtSignal()
    
    def __init__(self, title, description, icon_text="", enabled=True):
        super().__init__()
        self.enabled = enabled
        self.init_ui(title, description, icon_text)
    
    def init_ui(self, title, description, icon_text):
        """Initialize card UI"""
        self.setObjectName("dashboardCard")
        self.setFixedSize(200, 150)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Icon
        icon_label = QLabel(icon_text)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setObjectName("cardIcon")
        layout.addWidget(icon_label)
        
        # Title
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("cardTitle")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setObjectName("cardDescription")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Set enabled state
        self.setEnabled(self.enabled)
    
    def mousePressEvent(self, event):
        """Handle mouse press event"""
        if self.enabled and event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class MainDashboard(QMainWindow):
    def __init__(self, auth_manager: AuthenticationManager):
        super().__init__()
        self.auth_manager = auth_manager
        self.current_user = auth_manager.get_current_user()
        
        # Session timer
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self.check_session)
        self.session_timer.start(60000)  # Check every minute
        
        self.init_ui()
        self.load_dashboard_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(f"{APP_NAME} - Dashboard")
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        self.setStyleSheet(self.get_stylesheet())
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Header
        self.create_header(main_layout)
        
        # Content area
        self.create_content_area(main_layout)
        
        # Create status bar
        self.create_status_bar()
        
        # Center window
        self.center_window()
    
    def center_window(self):
        """Center the window on screen"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        logout_action = QAction('Logout', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        
        if self.auth_manager.has_permission('all'):
            user_mgmt_action = QAction('User Management', self)
            user_mgmt_action.triggered.connect(self.open_user_management)
            tools_menu.addAction(user_mgmt_action)
        
        change_password_action = QAction('Change Password', self)
        change_password_action.triggered.connect(self.change_password)
        tools_menu.addAction(change_password_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_header(self, layout):
        """Create header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Welcome message
        welcome_label = QLabel(f"Welcome, {self.current_user.full_name}")
        welcome_label.setObjectName("welcomeLabel")
        header_layout.addWidget(welcome_label)
        
        header_layout.addStretch()
        
        # User info
        user_info_layout = QVBoxLayout()
        
        role_label = QLabel(f"Role: {USER_ROLES.get(self.current_user.role, self.current_user.role)}")
        role_label.setObjectName("roleLabel")
        user_info_layout.addWidget(role_label)
        
        if self.current_user.last_login:
            last_login_label = QLabel(f"Last Login: {self.current_user.last_login.strftime('%Y-%m-%d %H:%M')}")
            last_login_label.setObjectName("lastLoginLabel")
            user_info_layout.addWidget(last_login_label)
        
        header_layout.addLayout(user_info_layout)
        
        layout.addWidget(header_frame)
    
    def create_content_area(self, layout):
        """Create main content area"""
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Dashboard cards
        left_panel = self.create_dashboard_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Quick info
        right_panel = self.create_info_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([800, 400])
        
        layout.addWidget(splitter)
    
    def create_dashboard_panel(self):
        """Create dashboard cards panel"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName("dashboardScrollArea")
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # Dashboard title
        title_label = QLabel("System Modules")
        title_label.setObjectName("sectionTitle")
        content_layout.addWidget(title_label)
        
        # Cards grid
        cards_layout = QGridLayout()
        cards_layout.setSpacing(15)
        
        # Define dashboard cards with permissions
        cards_data = [
            ("User Management", "Manage users and permissions", "👥", "all"),
            ("Inventory", "Manage materials and stock", "📦", "inventory_read"),
            ("Recipes", "Create and manage recipes", "📋", "recipe_read"),
            ("Sample Batches", "Test production batches", "🧪", "sample_all"),
            ("Production", "Manage production orders", "🏭", "production_read"),
            ("Quality Assurance", "QA tests and documents", "✅", "quality_read"),
            ("Reports", "Generate reports", "📊", "reporting_read"),
            ("Settings", "System configuration", "⚙️", "all")
        ]
        
        row, col = 0, 0
        for title, description, icon, permission in cards_data:
            # Check if user has permission for this card
            has_permission = (
                permission == "all" and self.auth_manager.has_permission("all") or
                self.auth_manager.has_permission(permission)
            )
            
            card = DashboardCard(title, description, icon, has_permission)
            card.clicked.connect(lambda t=title: self.open_module(t))
            
            cards_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 3:  # 3 cards per row
                col = 0
                row += 1
        
        content_layout.addLayout(cards_layout)
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        return scroll_area
    
    def create_info_panel(self):
        """Create information panel"""
        info_widget = QWidget()
        info_widget.setObjectName("infoPanel")
        info_layout = QVBoxLayout(info_widget)
        info_layout.setSpacing(15)
        info_layout.setContentsMargins(15, 15, 15, 15)
        
        # Quick Stats section
        stats_title = QLabel("Quick Statistics")
        stats_title.setObjectName("sectionTitle")
        info_layout.addWidget(stats_title)
        
        # Stats frame
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        stats_layout = QVBoxLayout(stats_frame)
        
        # Placeholder stats (will be loaded from database)
        self.stats_labels = {}
        stats_items = [
            ("Total Materials", "total_materials"),
            ("Low Stock Items", "low_stock"),
            ("Active Recipes", "active_recipes"),
            ("Production Batches Today", "batches_today"),
            ("Pending QA Tests", "pending_qa")
        ]
        
        for label_text, key in stats_items:
            stat_layout = QHBoxLayout()
            
            label = QLabel(label_text + ":")
            label.setObjectName("statLabel")
            stat_layout.addWidget(label)
            
            value_label = QLabel("Loading...")
            value_label.setObjectName("statValue")
            value_label.setAlignment(Qt.AlignRight)
            self.stats_labels[key] = value_label
            stat_layout.addWidget(value_label)
            
            stats_layout.addLayout(stat_layout)
        
        info_layout.addWidget(stats_frame)
        
        # Recent Activity section
        activity_title = QLabel("Recent Activity")
        activity_title.setObjectName("sectionTitle")
        info_layout.addWidget(activity_title)
        
        # Activity frame
        activity_frame = QFrame()
        activity_frame.setObjectName("activityFrame")
        activity_layout = QVBoxLayout(activity_frame)
        
        self.activity_label = QLabel("Loading recent activity...")
        self.activity_label.setObjectName("activityText")
        self.activity_label.setWordWrap(True)
        activity_layout.addWidget(self.activity_label)
        
        info_layout.addWidget(activity_frame)
        
        info_layout.addStretch()
        
        return info_widget
    
    def create_status_bar(self):
        """Create status bar"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # Connection status
        self.connection_label = QLabel("Database: Connected")
        self.connection_label.setObjectName("statusLabel")
        status_bar.addWidget(self.connection_label)
        
        status_bar.addPermanentWidget(QLabel(f"User: {self.current_user.username}"))
    
    def get_stylesheet(self):
        """Return the stylesheet for the main dashboard"""
        return f"""
            QMainWindow {{
                background-color: #f8f9fa;
            }}
            
            #headerFrame {{
                background-color: {THEME_COLOR};
                border-bottom: 2px solid #1e5f7a;
            }}
            
            #welcomeLabel {{
                color: white;
                font-size: 24px;
                font-weight: bold;
            }}
            
            #roleLabel, #lastLoginLabel {{
                color: #e6f3ff;
                font-size: 12px;
            }}
            
            #sectionTitle {{
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }}
            
            #dashboardCard {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 5px;
            }}
            
            #dashboardCard:hover {{
                border-color: {THEME_COLOR};
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
            
            #dashboardCard:disabled {{
                background-color: #f5f5f5;
                color: #999;
            }}
            
            #cardIcon {{
                font-size: 32px;
                margin-bottom: 5px;
            }}
            
            #cardTitle {{
                font-size: 14px;
                font-weight: bold;
                color: #333;
            }}
            
            #cardDescription {{
                font-size: 11px;
                color: #666;
            }}
            
            #infoPanel {{
                background-color: white;
                border-left: 1px solid #ddd;
            }}
            
            #statsFrame, #activityFrame {{
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 10px;
            }}
            
            #statLabel {{
                font-size: 12px;
                color: #666;
            }}
            
            #statValue {{
                font-size: 12px;
                font-weight: bold;
                color: {THEME_COLOR};
            }}
            
            #activityText {{
                font-size: 11px;
                color: #666;
                line-height: 1.4;
            }}
            
            #statusLabel {{
                color: #28a745;
                font-size: 11px;
            }}
            
            QMenuBar {{
                background-color: #f8f9fa;
                border-bottom: 1px solid #ddd;
            }}
            
            QMenuBar::item {{
                padding: 4px 8px;
                background-color: transparent;
            }}
            
            QMenuBar::item:selected {{
                background-color: {THEME_COLOR};
                color: white;
            }}
            
            QStatusBar {{
                background-color: #f8f9fa;
                border-top: 1px solid #ddd;
                font-size: 11px;
            }}
        """
    
    def load_dashboard_data(self):
        """Load dashboard statistics and recent activity"""
        # This would typically load data from the database
        # For now, we'll use placeholder data
        
        # Update stats (placeholder)
        self.stats_labels["total_materials"].setText("0")
        self.stats_labels["low_stock"].setText("0")
        self.stats_labels["active_recipes"].setText("0")
        self.stats_labels["batches_today"].setText("0")
        self.stats_labels["pending_qa"].setText("0")
        
        # Update activity (placeholder)
        self.activity_label.setText("No recent activity")
    
    def open_module(self, module_name):
        """Open specific module"""
        QMessageBox.information(
            self,
            "Module",
            f"Opening {module_name} module...\n\nThis feature will be implemented in the next phase."
        )
    
    def check_session(self):
        """Check if session is still valid"""
        if not self.auth_manager.is_session_valid():
            QMessageBox.warning(
                self,
                "Session Expired",
                "Your session has expired. Please log in again."
            )
            self.logout()
    
    def logout(self):
        """Logout and return to login screen"""
        self.auth_manager.logout()
        self.close()
        
        # Show login window again
        from gui.login_window import LoginWindow
        self.login_window = LoginWindow()
        self.login_window.show()
    
    def change_password(self):
        """Open change password dialog"""
        QMessageBox.information(
            self,
            "Change Password",
            "Change password feature will be implemented in the next phase."
        )
    
    def open_user_management(self):
        """Open user management window"""
        QMessageBox.information(
            self,
            "User Management",
            "User management feature will be implemented in the next phase."
        )
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About",
            f"{APP_NAME}\n\n"
            "A comprehensive production and quality management system\n"
            "for SFF manufacturing operations.\n\n"
            "Features:\n"
            "• User Management with Role-based Access\n"
            "• Inventory Management\n"
            "• Recipe Management\n"
            "• Production Planning\n"
            "• Quality Assurance\n"
            "• Comprehensive Reporting\n\n"
            "All data is securely stored in Microsoft Access database."
        )
