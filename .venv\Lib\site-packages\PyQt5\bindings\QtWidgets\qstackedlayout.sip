// qstackedlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStackedLayout : public QLayout
{
%TypeHeaderCode
#include <qstackedlayout.h>
%End

public:
    enum StackingMode
    {
        StackOne,
        StackAll,
    };

    QStackedLayout();
    explicit QStackedLayout(QWidget *parent /TransferThis/);
    explicit QStackedLayout(QLayout *parentLayout /TransferThis/);
    virtual ~QStackedLayout();
    int addWidget(QWidget *w /GetWrapper/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipCpp->addWidget(a0);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows addWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    int insertWidget(int index, QWidget *w /GetWrapper/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipCpp->insertWidget(a0, a1);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a1Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows insertWidget(QWidget()).
            sipTransferTo(a1Wrapper, sipSelf);
        }
%End

    QWidget *currentWidget() const;
    int currentIndex() const;
    QWidget *widget(int) const;
    virtual QWidget *widget();
    virtual int count() const;
    virtual void addItem(QLayoutItem *item /Transfer/);
    virtual QSize sizeHint() const;
    virtual QSize minimumSize() const;
    virtual QLayoutItem *itemAt(int) const;
    virtual QLayoutItem *takeAt(int) /TransferBack/;
    virtual void setGeometry(const QRect &rect);

signals:
    void widgetRemoved(int index);
    void currentChanged(int index);

public slots:
    void setCurrentIndex(int index);
    void setCurrentWidget(QWidget *w);

public:
    QStackedLayout::StackingMode stackingMode() const;
    void setStackingMode(QStackedLayout::StackingMode stackingMode);
    virtual bool hasHeightForWidth() const;
    virtual int heightForWidth(int width) const;
};
