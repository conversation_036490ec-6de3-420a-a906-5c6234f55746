"""
SFF Production & Quality Management System
Main Application Entry Point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from config.settings import APP_NAME, APP_VERSION, DATABASE_PATH
from gui.login_window import <PERSON><PERSON><PERSON><PERSON><PERSON>



def show_error_dialog(title, message):
    """Show error dialog"""
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec_()

def main():
    """Main application entry point"""
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName("SFF")

    try:
        # Check database
        if not os.path.exists(DATABASE_PATH):
            show_error_dialog("Database Error", f"Database file not found: {DATABASE_PATH}")
            return 1

        # Create and show login window directly
        login_window = LoginWindow()
        login_window.show()

        return app.exec_()

    except Exception as e:
        show_error_dialog("Startup Error", f"An unexpected error occurred during startup:\n\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
