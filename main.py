"""
SFF Production & Quality Management System
Main Application Entry Point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, Q<PERSON>ainter, QFont, QColor
from config.settings import APP_NAME, APP_VERSION, DATABASE_PATH
from database.create_database import create_database_schema
from gui.login_window import LoginWindow

class SplashScreen(QSplashScreen):
    """Custom splash screen for application startup"""
    
    def __init__(self):
        # Create a simple splash screen pixmap
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor("#2E86AB"))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor("white"))
        
        # Title
        title_font = QFont("Arial", 24, QFont.Bold)
        painter.setFont(title_font)
        painter.drawText(pixmap.rect(), Qt.Align<PERSON>enter, "SFF Production &\nQuality Management")
        
        # Version
        version_font = QFont("Arial", 12)
        painter.setFont(version_font)
        painter.drawText(20, 250, f"Version {APP_VERSION}")
        
        # Loading text
        painter.drawText(20, 270, "Loading...")
        
        painter.end()
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import pyodbc
    except ImportError:
        missing_deps.append("pyodbc")
    
    try:
        import bcrypt
    except ImportError:
        missing_deps.append("bcrypt")
    
    try:
        from PyQt5 import QtWidgets
    except ImportError:
        missing_deps.append("PyQt5")
    
    return missing_deps

def check_database():
    """Check if database exists and is accessible"""
    if not os.path.exists(DATABASE_PATH):
        return False, "Database file does not exist"
    
    try:
        import pyodbc
        conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={DATABASE_PATH};'
        conn = pyodbc.connect(conn_str)
        conn.close()
        return True, "Database is accessible"
    except Exception as e:
        return False, f"Database connection error: {str(e)}"

def initialize_database():
    """Initialize database if it doesn't exist"""
    if not os.path.exists(DATABASE_PATH):
        print("Database not found. Creating new database...")
        if create_database_schema():
            print("Database created successfully!")
            return True
        else:
            print("Failed to create database!")
            return False
    return True

def show_error_dialog(title, message):
    """Show error dialog"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec_()

def main():
    """Main application entry point"""
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName("SFF")
    
    # Show splash screen
    splash = SplashScreen()
    splash.show()
    app.processEvents()
    
    try:
        # Check dependencies
        splash.showMessage("Checking dependencies...", Qt.AlignBottom | Qt.AlignCenter, QColor("white"))
        app.processEvents()
        
        missing_deps = check_dependencies()
        if missing_deps:
            splash.close()
            show_error_dialog(
                "Missing Dependencies",
                f"The following required packages are missing:\n\n" +
                "\n".join(f"• {dep}" for dep in missing_deps) +
                f"\n\nPlease install them using:\npip install {' '.join(missing_deps)}"
            )
            return 1
        
        # Check Microsoft Access driver
        splash.showMessage("Checking database driver...", Qt.AlignBottom | Qt.AlignCenter, QColor("white"))
        app.processEvents()
        
        try:
            import pyodbc
            drivers = [driver for driver in pyodbc.drivers() if 'Access' in driver]
            if not drivers:
                splash.close()
                show_error_dialog(
                    "Database Driver Missing",
                    "Microsoft Access Database Engine is not installed.\n\n"
                    "Please download and install:\n"
                    "• Microsoft Access Database Engine 2016 Redistributable\n"
                    "• Or Microsoft Office with Access\n\n"
                    "Download from: https://www.microsoft.com/en-us/download/details.aspx?id=54920"
                )
                return 1
        except Exception as e:
            splash.close()
            show_error_dialog("Driver Check Error", f"Error checking database drivers: {str(e)}")
            return 1
        
        # Initialize database
        splash.showMessage("Initializing database...", Qt.AlignBottom | Qt.AlignCenter, QColor("white"))
        app.processEvents()
        
        if not initialize_database():
            splash.close()
            show_error_dialog(
                "Database Initialization Failed",
                "Failed to initialize the database.\n\n"
                "Please check:\n"
                "• Write permissions in the application directory\n"
                "• Microsoft Access driver installation\n"
                "• Available disk space"
            )
            return 1
        
        # Check database connectivity
        splash.showMessage("Testing database connection...", Qt.AlignBottom | Qt.AlignCenter, QColor("white"))
        app.processEvents()
        
        db_ok, db_message = check_database()
        if not db_ok:
            splash.close()
            show_error_dialog("Database Error", db_message)
            return 1
        
        # All checks passed, show login window
        splash.showMessage("Starting application...", Qt.AlignBottom | Qt.AlignCenter, QColor("white"))
        app.processEvents()
        
        # Small delay to show the splash screen
        QTimer.singleShot(1000, lambda: start_application(splash))
        
        return app.exec_()
        
    except Exception as e:
        splash.close()
        show_error_dialog("Startup Error", f"An unexpected error occurred during startup:\n\n{str(e)}")
        return 1

def start_application(splash):
    """Start the main application after splash screen"""
    try:
        # Close splash screen
        splash.close()
        
        # Create and show login window
        login_window = LoginWindow()
        login_window.show()
        
    except Exception as e:
        show_error_dialog("Application Error", f"Failed to start application:\n\n{str(e)}")

if __name__ == "__main__":
    sys.exit(main())
