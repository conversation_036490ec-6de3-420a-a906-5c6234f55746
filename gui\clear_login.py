"""
Clear and Readable Login Window for SFF Production System
Large fonts, clear text, and big buttons
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ClearLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize clear and readable login interface"""
        self.setWindowTitle("SFF Production System - Login")
        self.setFixedSize(600, 500)
        
        # Apply clear design with large fonts
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QWidget {
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #mainContainer {
                background-color: white;
                border: 3px solid #228B22;
                border-radius: 10px;
            }
            #headerSection {
                background-color: #228B22;
                border-radius: 10px 10px 0px 0px;
                padding: 30px;
            }
            #title {
                font-size: 32px;
                font-weight: bold;
                color: white;
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #subtitle {
                font-size: 18px;
                color: white;
                font-family: 'Arial Black', 'Arial', sans-serif;
                margin-top: 10px;
            }
            #formSection {
                padding: 40px;
                background-color: white;
            }
            #fieldLabel {
                font-size: 20px;
                color: #228B22;
                font-weight: bold;
                margin-bottom: 15px;
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            QLineEdit {
                padding: 20px 25px;
                border: 4px solid #90EE90;
                border-radius: 8px;
                font-size: 18px;
                background-color: white;
                margin-bottom: 25px;
                font-family: 'Arial', sans-serif;
                color: #333;
                font-weight: bold;
            }
            QLineEdit:focus {
                border-color: #228B22;
                background-color: #f8fff8;
                outline: none;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: normal;
                font-weight: normal;
            }
            #loginButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 25px 40px;
                border-radius: 10px;
                font-size: 22px;
                font-weight: bold;
                margin-top: 20px;
                font-family: 'Arial Black', 'Arial', sans-serif;
                min-height: 30px;
            }
            #loginButton:hover {
                background-color: #32CD32;
            }
            #loginButton:pressed {
                background-color: #006400;
            }
            #statusLabel {
                color: #dc3545;
                font-weight: bold;
                margin-top: 20px;
                font-size: 16px;
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #infoLabel {
                color: #666;
                font-size: 14px;
                margin-top: 25px;
                font-family: 'Arial', sans-serif;
                font-weight: bold;
            }
        """)
        
        # Center the window
        self.center_window()
        
        # Create main container
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(50, 50, 50, 50)
        main_layout.setSpacing(0)
        
        # Create main container frame
        container = QFrame()
        container.setObjectName("mainContainer")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # Header section
        header_section = QFrame()
        header_section.setObjectName("headerSection")
        header_layout = QVBoxLayout(header_section)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setAlignment(Qt.AlignCenter)
        
        title = QLabel("SFF PRODUCTION SYSTEM")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        subtitle = QLabel("LOGIN TO CONTINUE")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle)
        
        container_layout.addWidget(header_section)
        
        # Form section
        form_section = QFrame()
        form_section.setObjectName("formSection")
        form_layout = QVBoxLayout(form_section)
        form_layout.setSpacing(10)
        
        # Username field
        username_label = QLabel("USERNAME:")
        username_label.setObjectName("fieldLabel")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter username here")
        self.username_input.setText("admin")
        form_layout.addWidget(self.username_input)
        
        # Password field
        password_label = QLabel("PASSWORD:")
        password_label.setObjectName("fieldLabel")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter password here")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        form_layout.addWidget(self.password_input)
        
        # Login button
        self.login_button = QPushButton("LOGIN")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login_clicked)
        form_layout.addWidget(self.login_button)
        
        # Status message
        self.status_label = QLabel("")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(self.status_label)
        
        # Info label
        info_label = QLabel("Default: admin / admin123")
        info_label.setObjectName("infoLabel")
        info_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(info_label)
        
        container_layout.addWidget(form_section)
        main_layout.addWidget(container)
        
        # Set focus and connect Enter key
        self.username_input.setFocus()
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login_clicked(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # Clear previous status
        self.status_label.setText("")
        
        # Validate input
        if not username:
            self.show_status("PLEASE ENTER USERNAME")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("PLEASE ENTER PASSWORD")
            self.password_input.setFocus()
            return
        
        # Validate credentials
        if username == "admin" and password == "admin123":
            self.show_status("LOGIN SUCCESS!", "success")
            
            # Show success message
            QMessageBox.information(
                self, 
                "LOGIN SUCCESS", 
                f"WELCOME TO SFF PRODUCTION SYSTEM!\n\n"
                f"USER: {username.upper()}\n"
                f"ROLE: SYSTEM ADMINISTRATOR\n\n"
                f"OPENING MAIN DASHBOARD..."
            )
            
            # Open dashboard
            self.open_dashboard()
        else:
            self.show_status("WRONG USERNAME OR PASSWORD")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, status_type="error"):
        """Show status message"""
        self.status_label.setText(message)
        
        if status_type == "success":
            self.status_label.setStyleSheet("color: #228B22; font-weight: bold; font-size: 16px;")
        else:
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold; font-size: 16px;")
    
    def open_dashboard(self):
        """Open main dashboard"""
        try:
            from gui.clear_dashboard import ClearDashboard
            self.dashboard = ClearDashboard()
            self.dashboard.show()
            self.close()
        except Exception as e:
            QMessageBox.information(
                self,
                "LOGIN SUCCESS",
                f"LOGIN SUCCESSFUL!\n\n"
                f"SYSTEM IS READY.\n"
                f"DASHBOARD WILL OPEN SHORTLY.\n\n"
                f"NOTE: {str(e)}"
            )

def main():
    """Run the clear login window"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")
    
    window = ClearLoginWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
