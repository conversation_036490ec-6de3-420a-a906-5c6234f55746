"""
Improved Login Window for SFF Production System
Professional design with clear fonts and proper alignment
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ImprovedLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the improved login interface"""
        self.setWindowTitle("SFF Production & Quality Management System")
        self.setFixedSize(500, 400)
        
        # Apply professional design with clear fonts
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QWidget {
                font-family: 'Calibri', 'Arial', sans-serif;
            }
            #mainContainer {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            #headerSection {
                background-color: #228B22;
                border-radius: 8px 8px 0px 0px;
                padding: 25px;
            }
            #title {
                font-size: 24px;
                font-weight: bold;
                color: white;
                font-family: 'Calibri', 'Arial', sans-serif;
            }
            #subtitle {
                font-size: 14px;
                color: #e8f5e8;
                font-family: 'Calibri', 'Arial', sans-serif;
                margin-top: 5px;
            }
            #formSection {
                padding: 30px;
                background-color: white;
            }
            #fieldLabel {
                font-size: 14px;
                color: #2d5a2d;
                font-weight: 600;
                margin-bottom: 8px;
                font-family: 'Calibri', 'Arial', sans-serif;
            }
            QLineEdit {
                padding: 12px 15px;
                border: 2px solid #90EE90;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
                margin-bottom: 15px;
                font-family: 'Calibri', 'Arial', sans-serif;
                color: #333;
            }
            QLineEdit:focus {
                border-color: #228B22;
                background-color: #f8fff8;
                outline: none;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: italic;
            }
            #loginButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 14px 20px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
                margin-top: 10px;
                font-family: 'Calibri', 'Arial', sans-serif;
            }
            #loginButton:hover {
                background-color: #32CD32;
            }
            #loginButton:pressed {
                background-color: #006400;
            }
            #statusLabel {
                color: #dc3545;
                font-weight: 600;
                margin-top: 15px;
                font-size: 13px;
                font-family: 'Calibri', 'Arial', sans-serif;
            }
            #infoLabel {
                color: #666;
                font-size: 12px;
                margin-top: 20px;
                font-family: 'Calibri', 'Arial', sans-serif;
            }
        """)
        
        # Center the window
        self.center_window()
        
        # Create main container
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(0)
        
        # Create main container frame
        container = QFrame()
        container.setObjectName("mainContainer")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # Header section
        header_section = QFrame()
        header_section.setObjectName("headerSection")
        header_layout = QVBoxLayout(header_section)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        title = QLabel("SFF Production System")
        title.setObjectName("title")
        header_layout.addWidget(title)
        
        subtitle = QLabel("Production & Quality Management System")
        subtitle.setObjectName("subtitle")
        header_layout.addWidget(subtitle)
        
        container_layout.addWidget(header_section)
        
        # Form section
        form_section = QFrame()
        form_section.setObjectName("formSection")
        form_layout = QVBoxLayout(form_section)
        form_layout.setSpacing(0)
        
        # Username field
        username_label = QLabel("Username")
        username_label.setObjectName("fieldLabel")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your username")
        self.username_input.setText("admin")
        form_layout.addWidget(self.username_input)
        
        # Password field
        password_label = QLabel("Password")
        password_label.setObjectName("fieldLabel")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        form_layout.addWidget(self.password_input)
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login_clicked)
        form_layout.addWidget(self.login_button)
        
        # Status message
        self.status_label = QLabel("")
        self.status_label.setObjectName("statusLabel")
        form_layout.addWidget(self.status_label)
        
        # Info label
        info_label = QLabel("Default credentials: admin / admin123")
        info_label.setObjectName("infoLabel")
        form_layout.addWidget(info_label)
        
        container_layout.addWidget(form_section)
        main_layout.addWidget(container)
        
        # Set focus and connect Enter key
        self.username_input.setFocus()
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login_clicked(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # Clear previous status
        self.status_label.setText("")
        
        # Validate input
        if not username:
            self.show_status("Please enter your username")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("Please enter your password")
            self.password_input.setFocus()
            return
        
        # Validate credentials
        if username == "admin" and password == "admin123":
            self.show_status("Login successful!", "success")
            
            # Show success message
            QMessageBox.information(
                self, 
                "Login Successful", 
                f"Welcome to SFF Production & Quality Management System!\n\n"
                f"User: {username}\n"
                f"Role: System Administrator\n\n"
                f"Opening main dashboard..."
            )
            
            # Open dashboard
            self.open_dashboard()
        else:
            self.show_status("Invalid username or password")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, status_type="error"):
        """Show status message"""
        self.status_label.setText(message)
        
        if status_type == "success":
            self.status_label.setStyleSheet("color: #228B22; font-weight: 600;")
        else:
            self.status_label.setStyleSheet("color: #dc3545; font-weight: 600;")
    
    def open_dashboard(self):
        """Open main dashboard"""
        try:
            from gui.improved_dashboard import ImprovedDashboard
            self.dashboard = ImprovedDashboard()
            self.dashboard.show()
            self.close()
        except Exception as e:
            QMessageBox.information(
                self,
                "Login Successful",
                f"Login successful!\n\n"
                f"System is ready for use.\n"
                f"Dashboard will open shortly.\n\n"
                f"Note: {str(e)}"
            )

def main():
    """Run the improved login window"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")
    
    window = ImprovedLoginWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
