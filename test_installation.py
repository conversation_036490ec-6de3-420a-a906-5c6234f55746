"""
SFF Production & Quality Management System
Installation Test Script
"""

import sys
import os
from datetime import datetime

def test_python_version():
    """Test Python version compatibility"""
    print("Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_dependencies():
    """Test required Python packages"""
    print("\nTesting Python dependencies...")
    
    required_packages = [
        'PyQt5',
        'pyodbc',
        'pandas',
        'openpyxl',
        'reportlab',
        'PIL',  # Pillow
        'bcrypt',
        'cryptography',
        'dateutil',
        'matplotlib',
        'numpy',
        'xlsxwriter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                print(f"✅ Pillow - Available")
            elif package == 'dateutil':
                import dateutil
                print(f"✅ python-dateutil - Available")
            else:
                __import__(package)
                print(f"✅ {package} - Available")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ All required packages are available")
        return True

def test_database_driver():
    """Test Microsoft Access database driver"""
    print("\nTesting database driver...")
    
    try:
        import pyodbc
        drivers = [driver for driver in pyodbc.drivers() if 'Access' in driver]
        
        if drivers:
            print(f"✅ Microsoft Access driver found: {drivers[0]}")
            return True
        else:
            print("❌ Microsoft Access driver not found")
            print("Please install Microsoft Access Database Engine 2016 Redistributable")
            print("Download from: https://www.microsoft.com/en-us/download/details.aspx?id=54920")
            return False
            
    except Exception as e:
        print(f"❌ Error checking database driver: {str(e)}")
        return False

def test_database_creation():
    """Test database creation and connectivity"""
    print("\nTesting database creation...")
    
    try:
        from database.create_database import create_database_schema
        from config.settings import DATABASE_PATH
        
        # Remove existing database for clean test
        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)
            print("Removed existing test database")
        
        # Create database
        if create_database_schema():
            print("✅ Database created successfully")
            
            # Test connectivity
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            if db_manager.connect():
                print("✅ Database connection successful")
                db_manager.disconnect()
                return True
            else:
                print("❌ Database connection failed")
                return False
        else:
            print("❌ Database creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test error: {str(e)}")
        return False

def test_authentication():
    """Test authentication system"""
    print("\nTesting authentication system...")
    
    try:
        from auth.authentication import AuthenticationManager
        from database.db_manager import DatabaseManager
        
        auth_manager = AuthenticationManager()
        
        # Test login with default admin credentials
        result = auth_manager.login("admin", "admin123")
        
        if result['success']:
            print("✅ Authentication system working")
            print(f"✅ Default admin user login successful")
            
            # Test session validation
            if auth_manager.is_session_valid():
                print("✅ Session management working")
            else:
                print("❌ Session management failed")
                return False
            
            # Test logout
            auth_manager.logout()
            print("✅ Logout successful")
            
            return True
        else:
            print(f"❌ Authentication failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test error: {str(e)}")
        return False

def test_gui_components():
    """Test GUI components"""
    print("\nTesting GUI components...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # Create QApplication instance (required for GUI testing)
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test login window
        from gui.login_window import LoginWindow
        login_window = LoginWindow()
        print("✅ Login window created successfully")
        
        # Test main dashboard (requires authentication)
        from auth.authentication import AuthenticationManager
        auth_manager = AuthenticationManager()
        auth_result = auth_manager.login("admin", "admin123")
        
        if auth_result['success']:
            from gui.main_dashboard import MainDashboard
            dashboard = MainDashboard(auth_manager)
            print("✅ Main dashboard created successfully")
            
            # Test inventory management
            from gui.inventory_management import InventoryManagement
            inventory_widget = InventoryManagement(auth_manager)
            print("✅ Inventory management widget created successfully")
            
            auth_manager.logout()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI test error: {str(e)}")
        return False

def test_pdf_generation():
    """Test PDF generation"""
    print("\nTesting PDF generation...")
    
    try:
        from utils.pdf_generator import PDFGenerator
        import tempfile
        import os
        
        pdf_gen = PDFGenerator()
        
        # Test MSDS generation
        batch_data = {
            'product_name': 'Test Product',
            'batch_number': 'TEST001',
            'manufacturing_date': '2024-01-01',
            'ingredients': [
                {
                    'name': 'Water',
                    'cas_number': '7732-18-5',
                    'percentage': 80.0,
                    'hazard_class': 'Non-hazardous'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            if pdf_gen.generate_msds(batch_data, temp_file.name):
                print("✅ MSDS PDF generation successful")
                os.unlink(temp_file.name)  # Clean up
                return True
            else:
                print("❌ MSDS PDF generation failed")
                return False
                
    except Exception as e:
        print(f"❌ PDF generation test error: {str(e)}")
        return False

def run_all_tests():
    """Run all installation tests"""
    print("=" * 60)
    print("SFF Production & Quality Management System")
    print("Installation Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("Database Driver", test_database_driver),
        ("Database Creation", test_database_creation),
        ("Authentication", test_authentication),
        ("GUI Components", test_gui_components),
        ("PDF Generation", test_pdf_generation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {str(e)}")
            print()
    
    print("=" * 60)
    print(f"Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Installation is successful.")
        print("\nYou can now run the application with: python main.py")
        print("Default login: admin / admin123")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("Refer to the README.md file for installation instructions.")
    
    print("=" * 60)
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
