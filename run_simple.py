"""
SFF Production System - Simple Launcher
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox

def main():
    """Run the application"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")

    try:
        # Check if database exists
        db_path = os.path.join(os.getcwd(), "SFF_Production_Database.accdb")
        if not os.path.exists(db_path):
            QMessageBox.critical(
                None,
                "Database Error",
                f"Database not found:\n{db_path}\n\nPlease ensure the file exists."
            )
            return 1

        # Import and launch login window
        from gui.ultra_clear_login import UltraClearLoginWindow

        login_window = UltraClearLoginWindow()
        login_window.show()
        login_window.raise_()
        login_window.activateWindow()

        return app.exec_()

    except ImportError as e:
        QMessageBox.critical(
            None,
            "Import Error",
            f"Failed to import login module:\n\n{str(e)}\n\nPlease check if all files are present."
        )
        return 1

    except Exception as e:
        QMessageBox.critical(
            None,
            "Application Error",
            f"An error occurred while starting the application:\n\n{str(e)}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
