"""
SFF Production System - Simple Launcher
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt

class SimpleLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SFF Production System - Login")
        self.setFixedSize(400, 300)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create layout
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)

        # Title
        title = QLabel("SFF Production System")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #228B22; margin-bottom: 20px;")
        layout.addWidget(title)

        # Username
        username_label = QLabel("Username:")
        username_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #000;")
        layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setText("admin")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #228B22;
                border-radius: 5px;
                font-size: 14px;
                color: #000;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.username_input)

        # Password
        password_label = QLabel("Password:")
        password_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #000;")
        layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #228B22;
                border-radius: 5px;
                font-size: 14px;
                color: #000;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.password_input)

        # Login button
        login_button = QPushButton("Login")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #32CD32;
            }
        """)
        login_button.clicked.connect(self.login_clicked)
        layout.addWidget(login_button)

        # Info
        info = QLabel("Default: admin / admin123")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("font-size: 12px; color: #666; margin-top: 10px;")
        layout.addWidget(info)

        # Connect Enter key
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)

    def login_clicked(self):
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if username == "admin" and password == "admin123":
            QMessageBox.information(
                self,
                "Login Successful",
                "Welcome to SFF Production System!\n\n"
                "User: admin\n"
                "Role: System Administrator\n\n"
                "System is ready for use."
            )
        else:
            QMessageBox.warning(
                self,
                "Login Failed",
                "Invalid username or password.\n\n"
                "Please use: admin / admin123"
            )

def main():
    """Run the application"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")

    try:
        # Check if database exists
        db_path = os.path.join(os.getcwd(), "SFF_Production_Database.accdb")
        if not os.path.exists(db_path):
            QMessageBox.critical(
                None,
                "Database Error",
                f"Database not found:\n{db_path}\n\nPlease ensure the file exists."
            )
            return 1

        # Create and show login window
        window = SimpleLoginWindow()
        window.show()

        return app.exec_()

    except Exception as e:
        QMessageBox.critical(
            None,
            "Application Error",
            f"An error occurred:\n\n{str(e)}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
