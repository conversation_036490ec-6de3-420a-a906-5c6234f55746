"""
تشغيل نظام SFF بواجهة مبسطة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox

def main():
    """تشغيل البرنامج"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production & Quality Management System")
    
    try:
        # التحقق من وجود قاعدة البيانات
        db_path = os.path.join(os.getcwd(), "SFF_Production_Database.accdb")
        if not os.path.exists(db_path):
            QMessageBox.critical(
                None,
                "خطأ في قاعدة البيانات",
                f"لم يتم العثور على قاعدة البيانات:\n{db_path}\n\nيرجى التأكد من وجود الملف."
            )
            return 1
        
        # تشغيل نافذة تسجيل الدخول المبسطة
        from gui.simple_login import SimpleLoginWindow
        
        login_window = SimpleLoginWindow()
        login_window.show()
        
        return app.exec_()
        
    except Exception as e:
        QMessageBox.critical(
            None,
            "خطأ في التشغيل",
            f"حدث خطأ أثناء تشغيل البرنامج:\n\n{str(e)}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
