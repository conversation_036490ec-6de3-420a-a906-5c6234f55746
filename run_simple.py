"""
SFF Production System - Simple Launcher
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt

class SimpleLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SFF Production System - Login")
        self.setFixedSize(450, 350)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create layout
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)

        # Title
        title = QLabel("SFF Production System")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #228B22; margin-bottom: 20px;")
        layout.addWidget(title)

        # Username
        username_label = QLabel("Username:")
        username_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #000; margin-bottom: 5px;")
        layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setText("admin")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 3px solid #228B22;
                border-radius: 6px;
                font-size: 16px;
                color: #000000;
                font-weight: bold;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #006400;
            }
        """)
        layout.addWidget(self.username_input)

        # Password
        password_label = QLabel("Password:")
        password_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #000; margin-bottom: 5px;")
        layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 3px solid #228B22;
                border-radius: 6px;
                font-size: 16px;
                color: #000000;
                font-weight: bold;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #006400;
            }
        """)
        layout.addWidget(self.password_input)

        # Login button
        login_button = QPushButton("Login")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 6px;
                font-size: 18px;
                font-weight: bold;
                margin-top: 10px;
            }
            QPushButton:hover {
                background-color: #32CD32;
            }
            QPushButton:pressed {
                background-color: #006400;
            }
        """)
        login_button.clicked.connect(self.login_clicked)
        layout.addWidget(login_button)

        # Info
        info = QLabel("Default: admin / admin123")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("font-size: 12px; color: #666; margin-top: 10px;")
        layout.addWidget(info)

        # Connect Enter key
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)

    def login_clicked(self):
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if username == "admin" and password == "admin123":
            # Hide login window
            self.hide()

            # Try to open dashboard
            try:
                self.open_dashboard()
            except:
                # If dashboard fails, show success message
                QMessageBox.information(
                    self,
                    "Login Successful",
                    "Welcome to SFF Production System!\n\n"
                    "User: admin\n"
                    "Role: System Administrator\n\n"
                    "System is ready for use."
                )
                self.show()  # Show login window again
        else:
            QMessageBox.warning(
                self,
                "Login Failed",
                "Invalid username or password.\n\n"
                "Please use: admin / admin123"
            )

    def open_dashboard(self):
        """Open the main dashboard"""
        try:
            # Try to import and create dashboard
            from gui.perfect_dashboard import PerfectDashboard
            self.dashboard = PerfectDashboard()
            self.dashboard.show()
            self.dashboard.raise_()
            self.dashboard.activateWindow()
            self.close()  # Close login window

        except ImportError:
            # Create a simple dashboard if perfect_dashboard not found
            self.create_simple_dashboard()

        except Exception as e:
            # If any error, create simple dashboard
            self.create_simple_dashboard()

    def create_simple_dashboard(self):
        """Create a simple dashboard window"""
        from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton, QGridLayout

        self.dashboard = QMainWindow()
        self.dashboard.setWindowTitle("SFF Production System - Dashboard")
        self.dashboard.setGeometry(100, 100, 800, 600)

        # Create central widget
        central_widget = QWidget()
        self.dashboard.setCentralWidget(central_widget)

        # Create layout
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Header
        header = QLabel("SFF Production & Quality Management System")
        header.setStyleSheet("font-size: 24px; font-weight: bold; color: #228B22; margin-bottom: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)

        # Welcome message
        welcome = QLabel("Welcome, Administrator")
        welcome.setStyleSheet("font-size: 18px; color: #333; margin-bottom: 30px;")
        welcome.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome)

        # Modules grid
        modules_widget = QWidget()
        modules_layout = QGridLayout(modules_widget)
        modules_layout.setSpacing(15)

        # Define modules
        modules = [
            ("User Management", "Manage system users"),
            ("Inventory Management", "Track materials and stock"),
            ("Recipe Management", "Create production recipes"),
            ("Production Management", "Manage production orders"),
            ("Quality Assurance", "QA tests and documents"),
            ("Reports", "Generate system reports")
        ]

        # Create module buttons
        row, col = 0, 0
        for title, description in modules:
            button = QPushButton(f"{title}\n{description}")
            button.setStyleSheet("""
                QPushButton {
                    background-color: #f8fff8;
                    border: 2px solid #228B22;
                    border-radius: 8px;
                    padding: 20px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #228B22;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: #228B22;
                    color: white;
                }
            """)
            button.setFixedSize(200, 100)
            button.clicked.connect(lambda _, t=title: self.open_module(t))
            modules_layout.addWidget(button, row, col)

            col += 1
            if col >= 3:  # 3 buttons per row
                col = 0
                row += 1

        layout.addWidget(modules_widget)

        # Logout button
        logout_button = QPushButton("Logout")
        logout_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        logout_button.clicked.connect(self.logout)
        layout.addWidget(logout_button)

        # Show dashboard
        self.dashboard.show()
        self.dashboard.raise_()
        self.dashboard.activateWindow()
        self.close()  # Close login window

    def open_module(self, module_name):
        """Open a specific module"""
        QMessageBox.information(
            self.dashboard,
            "Module",
            f"Opening {module_name}...\n\n"
            f"This module will be available in the next update."
        )

    def logout(self):
        """Logout and return to login"""
        reply = QMessageBox.question(
            self.dashboard,
            "Logout",
            "Are you sure you want to logout?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.dashboard.close()
            self.show()  # Show login window again

def main():
    """Run the application"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")

    try:
        # Check if database exists
        db_path = os.path.join(os.getcwd(), "SFF_Production_Database.accdb")
        if not os.path.exists(db_path):
            QMessageBox.critical(
                None,
                "Database Error",
                f"Database not found:\n{db_path}\n\nPlease ensure the file exists."
            )
            return 1

        # Create and show login window
        window = SimpleLoginWindow()
        window.show()
        window.raise_()
        window.activateWindow()

        print("SFF Production System started successfully!")
        print("Login window should now be visible.")
        print("Use: admin / admin123 to login")

        return app.exec_()

    except Exception as e:
        QMessageBox.critical(
            None,
            "Application Error",
            f"An error occurred:\n\n{str(e)}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
