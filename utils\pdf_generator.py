"""
SFF Production & Quality Management System
PDF Document Generation Utilities
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
from typing import Dict, List, Any
import os

class PDFGenerator:
    """PDF document generator for quality documents and reports"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles"""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2E86AB')
        ))
        
        # Header style
        self.styles.add(ParagraphStyle(
            name='CustomHeader',
            parent=self.styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.HexColor('#2E86AB')
        ))
        
        # Subheader style
        self.styles.add(ParagraphStyle(
            name='CustomSubHeader',
            parent=self.styles['Heading2'],
            fontSize=12,
            spaceAfter=8,
            textColor=colors.HexColor('#333333')
        ))
        
        # Footer style
        self.styles.add(ParagraphStyle(
            name='Footer',
            parent=self.styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        ))
    
    def generate_msds(self, batch_data: Dict[str, Any], output_path: str) -> bool:
        """Generate Material Safety Data Sheet (MSDS)"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            
            # Title
            title = Paragraph("MATERIAL SAFETY DATA SHEET (MSDS)", self.styles['CustomTitle'])
            story.append(title)
            story.append(Spacer(1, 20))
            
            # Product Information
            story.append(Paragraph("1. PRODUCT IDENTIFICATION", self.styles['CustomHeader']))
            
            product_info = [
                ['Product Name:', batch_data.get('product_name', 'N/A')],
                ['Batch Number:', batch_data.get('batch_number', 'N/A')],
                ['Manufacturing Date:', batch_data.get('manufacturing_date', 'N/A')],
                ['Manufacturer:', 'SFF Production'],
                ['Emergency Contact:', '+1-XXX-XXX-XXXX']
            ]
            
            product_table = Table(product_info, colWidths=[2*inch, 4*inch])
            product_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(product_table)
            story.append(Spacer(1, 20))
            
            # Composition/Information on Ingredients
            story.append(Paragraph("2. COMPOSITION/INFORMATION ON INGREDIENTS", self.styles['CustomHeader']))
            
            ingredients = batch_data.get('ingredients', [])
            if ingredients:
                ingredient_data = [['Ingredient', 'CAS Number', 'Percentage', 'Hazard Classification']]
                for ingredient in ingredients:
                    ingredient_data.append([
                        ingredient.get('name', 'N/A'),
                        ingredient.get('cas_number', 'N/A'),
                        f"{ingredient.get('percentage', 0):.2f}%",
                        ingredient.get('hazard_class', 'Non-hazardous')
                    ])
                
                ingredient_table = Table(ingredient_data, colWidths=[2*inch, 1.5*inch, 1*inch, 1.5*inch])
                ingredient_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(ingredient_table)
            else:
                story.append(Paragraph("No hazardous ingredients identified.", self.styles['Normal']))
            
            story.append(Spacer(1, 20))
            
            # Hazards Identification
            story.append(Paragraph("3. HAZARDS IDENTIFICATION", self.styles['CustomHeader']))
            story.append(Paragraph("This product is generally considered non-hazardous under normal conditions of use.", self.styles['Normal']))
            story.append(Spacer(1, 10))
            
            # First Aid Measures
            story.append(Paragraph("4. FIRST AID MEASURES", self.styles['CustomHeader']))
            first_aid_text = """
            <b>Eye Contact:</b> Rinse immediately with plenty of water for at least 15 minutes.<br/>
            <b>Skin Contact:</b> Wash with soap and water.<br/>
            <b>Inhalation:</b> Move to fresh air.<br/>
            <b>Ingestion:</b> Rinse mouth with water. Do not induce vomiting.
            """
            story.append(Paragraph(first_aid_text, self.styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Storage and Handling
            story.append(Paragraph("5. HANDLING AND STORAGE", self.styles['CustomHeader']))
            storage_text = """
            Store in a cool, dry place away from direct sunlight. Keep container tightly closed when not in use.
            Use appropriate personal protective equipment when handling.
            """
            story.append(Paragraph(storage_text, self.styles['Normal']))
            
            # Footer
            story.append(Spacer(1, 40))
            footer_text = f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | SFF Production & Quality Management System"
            story.append(Paragraph(footer_text, self.styles['Footer']))
            
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"Error generating MSDS: {str(e)}")
            return False
    
    def generate_coa(self, batch_data: Dict[str, Any], test_results: List[Dict], output_path: str) -> bool:
        """Generate Certificate of Analysis (COA)"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            
            # Title
            title = Paragraph("CERTIFICATE OF ANALYSIS", self.styles['CustomTitle'])
            story.append(title)
            story.append(Spacer(1, 20))
            
            # Product Information
            story.append(Paragraph("PRODUCT INFORMATION", self.styles['CustomHeader']))
            
            product_info = [
                ['Product Name:', batch_data.get('product_name', 'N/A')],
                ['Batch Number:', batch_data.get('batch_number', 'N/A')],
                ['Manufacturing Date:', batch_data.get('manufacturing_date', 'N/A')],
                ['Expiry Date:', batch_data.get('expiry_date', 'N/A')],
                ['Batch Size:', f"{batch_data.get('batch_size', 'N/A')} {batch_data.get('unit_of_measure', '')}"],
                ['Tested By:', batch_data.get('tested_by', 'N/A')],
                ['Test Date:', batch_data.get('test_date', 'N/A')]
            ]
            
            product_table = Table(product_info, colWidths=[2*inch, 4*inch])
            product_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(product_table)
            story.append(Spacer(1, 20))
            
            # Test Results
            story.append(Paragraph("TEST RESULTS", self.styles['CustomHeader']))
            
            if test_results:
                test_data = [['Test Parameter', 'Specification', 'Result', 'Status']]
                for test in test_results:
                    status_color = colors.green if test.get('status') == 'PASS' else colors.red
                    test_data.append([
                        test.get('parameter', 'N/A'),
                        test.get('specification', 'N/A'),
                        test.get('result', 'N/A'),
                        test.get('status', 'N/A')
                    ])
                
                test_table = Table(test_data, colWidths=[2*inch, 2*inch, 1.5*inch, 0.5*inch])
                test_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(test_table)
            else:
                story.append(Paragraph("No test results available.", self.styles['Normal']))
            
            story.append(Spacer(1, 30))
            
            # Conclusion
            story.append(Paragraph("CONCLUSION", self.styles['CustomHeader']))
            all_passed = all(test.get('status') == 'PASS' for test in test_results)
            conclusion = "This batch MEETS all specified requirements." if all_passed else "This batch DOES NOT MEET all specified requirements."
            story.append(Paragraph(conclusion, self.styles['Normal']))
            
            # Signature section
            story.append(Spacer(1, 40))
            signature_data = [
                ['Quality Manager:', '_' * 30, 'Date:', '_' * 20],
                ['', 'Signature', '', 'Date']
            ]
            signature_table = Table(signature_data, colWidths=[1.5*inch, 2*inch, 1*inch, 1.5*inch])
            signature_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(signature_table)
            
            # Footer
            story.append(Spacer(1, 20))
            footer_text = f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | SFF Production & Quality Management System"
            story.append(Paragraph(footer_text, self.styles['Footer']))
            
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"Error generating COA: {str(e)}")
            return False
    
    def generate_tds(self, batch_data: Dict[str, Any], specifications: List[Dict], output_path: str) -> bool:
        """Generate Technical Data Sheet (TDS)"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            
            # Title
            title = Paragraph("TECHNICAL DATA SHEET", self.styles['CustomTitle'])
            story.append(title)
            story.append(Spacer(1, 20))
            
            # Product Information
            story.append(Paragraph("PRODUCT INFORMATION", self.styles['CustomHeader']))
            
            product_info = [
                ['Product Name:', batch_data.get('product_name', 'N/A')],
                ['Product Code:', batch_data.get('product_code', 'N/A')],
                ['Version:', batch_data.get('version', '1.0')],
                ['Description:', batch_data.get('description', 'N/A')]
            ]
            
            product_table = Table(product_info, colWidths=[2*inch, 4*inch])
            product_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(product_table)
            story.append(Spacer(1, 20))
            
            # Technical Specifications
            story.append(Paragraph("TECHNICAL SPECIFICATIONS", self.styles['CustomHeader']))
            
            if specifications:
                spec_data = [['Parameter', 'Specification', 'Test Method']]
                for spec in specifications:
                    spec_data.append([
                        spec.get('parameter', 'N/A'),
                        spec.get('specification', 'N/A'),
                        spec.get('test_method', 'N/A')
                    ])
                
                spec_table = Table(spec_data, colWidths=[2*inch, 2*inch, 2*inch])
                spec_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(spec_table)
            else:
                story.append(Paragraph("No specifications available.", self.styles['Normal']))
            
            story.append(Spacer(1, 20))
            
            # Storage and Handling
            story.append(Paragraph("STORAGE AND HANDLING", self.styles['CustomHeader']))
            storage_text = batch_data.get('storage_instructions', 
                "Store in a cool, dry place away from direct sunlight. Keep container tightly closed when not in use.")
            story.append(Paragraph(storage_text, self.styles['Normal']))
            
            # Footer
            story.append(Spacer(1, 40))
            footer_text = f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | SFF Production & Quality Management System"
            story.append(Paragraph(footer_text, self.styles['Footer']))
            
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"Error generating TDS: {str(e)}")
            return False
    
    def generate_production_report(self, report_data: Dict[str, Any], output_path: str) -> bool:
        """Generate production report"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            
            # Title
            title = Paragraph("PRODUCTION REPORT", self.styles['CustomTitle'])
            story.append(title)
            story.append(Spacer(1, 20))
            
            # Report Information
            report_info = [
                ['Report Period:', f"{report_data.get('start_date', 'N/A')} to {report_data.get('end_date', 'N/A')}"],
                ['Generated By:', report_data.get('generated_by', 'N/A')],
                ['Generation Date:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
            ]
            
            info_table = Table(report_info, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(info_table)
            story.append(Spacer(1, 20))
            
            # Production Summary
            story.append(Paragraph("PRODUCTION SUMMARY", self.styles['CustomHeader']))
            
            batches = report_data.get('batches', [])
            if batches:
                batch_data = [['Batch Number', 'Product', 'Quantity', 'Status', 'Date']]
                for batch in batches:
                    batch_data.append([
                        batch.get('batch_number', 'N/A'),
                        batch.get('product_name', 'N/A'),
                        f"{batch.get('quantity', 'N/A')} {batch.get('unit', '')}",
                        batch.get('status', 'N/A'),
                        batch.get('date', 'N/A')
                    ])
                
                batch_table = Table(batch_data, colWidths=[1.5*inch, 2*inch, 1*inch, 1*inch, 1*inch])
                batch_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(batch_table)
            else:
                story.append(Paragraph("No production data available for this period.", self.styles['Normal']))
            
            # Footer
            story.append(Spacer(1, 40))
            footer_text = f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | SFF Production & Quality Management System"
            story.append(Paragraph(footer_text, self.styles['Footer']))
            
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"Error generating production report: {str(e)}")
            return False
