"""
SFF Production & Quality Management System
Data Models and Structures
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
from decimal import Decimal

@dataclass
class User:
    user_id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    email: str = ""
    full_name: str = ""
    role: str = ""
    is_active: bool = True
    created_date: Optional[datetime] = None
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None

@dataclass
class Material:
    material_id: Optional[int] = None
    material_code: str = ""
    material_name: str = ""
    material_type: str = ""  # RAW_INGREDIENT, FLAVOR, CHEMICAL, PACKAGING
    unit_of_measure: str = ""
    current_stock: Decimal = Decimal('0')
    minimum_threshold: Decimal = Decimal('0')
    unit_cost: Decimal = Decimal('0')
    supplier: str = ""
    storage_location: str = ""
    expiry_date: Optional[datetime] = None
    created_date: Optional[datetime] = None
    is_active: bool = True

@dataclass
class Recipe:
    recipe_id: Optional[int] = None
    recipe_code: str = ""
    recipe_name: str = ""
    version: str = "1.0"
    description: str = ""
    batch_size: Decimal = Decimal('0')
    unit_of_measure: str = ""
    total_cost: Decimal = Decimal('0')
    is_approved: bool = False
    created_by: int = 0
    created_date: Optional[datetime] = None
    approved_by: Optional[int] = None
    approved_date: Optional[datetime] = None
    is_active: bool = True

@dataclass
class RecipeIngredient:
    recipe_ingredient_id: Optional[int] = None
    recipe_id: int = 0
    material_id: int = 0
    quantity: Decimal = Decimal('0')
    unit_of_measure: str = ""
    percentage: Decimal = Decimal('0')
    notes: str = ""

@dataclass
class ProductionBatch:
    batch_id: Optional[int] = None
    batch_number: str = ""
    recipe_id: int = 0
    batch_type: str = "PRODUCTION"  # PRODUCTION, SAMPLE
    planned_quantity: Decimal = Decimal('0')
    actual_quantity: Decimal = Decimal('0')
    status: str = "PLANNED"  # PLANNED, IN_PROGRESS, COMPLETED, CANCELLED
    operator_id: int = 0
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    total_cost: Decimal = Decimal('0')
    notes: str = ""
    created_date: Optional[datetime] = None

@dataclass
class BatchIngredientUsage:
    usage_id: Optional[int] = None
    batch_id: int = 0
    material_id: int = 0
    planned_quantity: Decimal = Decimal('0')
    actual_quantity: Decimal = Decimal('0')
    unit_cost: Decimal = Decimal('0')
    total_cost: Decimal = Decimal('0')

@dataclass
class QualityTest:
    test_id: Optional[int] = None
    batch_id: int = 0
    test_type: str = ""
    test_parameter: str = ""
    specification: str = ""
    result: str = ""
    status: str = "PENDING"  # PENDING, PASS, FAIL
    tested_by: int = 0
    test_date: Optional[datetime] = None
    notes: str = ""

@dataclass
class QualityDocument:
    document_id: Optional[int] = None
    batch_id: int = 0
    document_type: str = ""  # MSDS, COA, TDS, SPEC_SHEET
    document_content: str = ""
    generated_by: int = 0
    generated_date: Optional[datetime] = None
    is_approved: bool = False
    approved_by: Optional[int] = None
    approved_date: Optional[datetime] = None

@dataclass
class InventoryMovement:
    movement_id: Optional[int] = None
    material_id: int = 0
    movement_type: str = ""  # IN, OUT, ADJUSTMENT, PRODUCTION_USE
    quantity: Decimal = Decimal('0')
    reference_id: Optional[int] = None  # batch_id for production use
    reference_type: str = ""  # BATCH, PURCHASE, ADJUSTMENT
    performed_by: int = 0
    movement_date: Optional[datetime] = None
    notes: str = ""

@dataclass
class AuditLog:
    log_id: Optional[int] = None
    user_id: int = 0
    action: str = ""
    table_name: str = ""
    record_id: Optional[int] = None
    old_values: str = ""
    new_values: str = ""
    timestamp: Optional[datetime] = None
    ip_address: str = ""
