// qlayoutitem.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLayoutItem /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qlayoutitem.h>
%End

%ConvertToSubClassCode
    if (sipCpp->widget())
    {
        sipType = sipType_QWidgetItem;
    }
    else if (sipCpp->spacerItem())
    {
        sipType = sipType_QSpacerItem;
    }
    else
    {
        // Switch to the QObject convertor.
        *sipCppRet = sipCpp->layout();
        sipType = sipType_QObject;
    }
%End

public:
    explicit QLayoutItem(Qt::Alignment alignment = Qt::Alignment());
    virtual ~QLayoutItem();
    virtual QSize sizeHint() const = 0;
    virtual QSize minimumSize() const = 0;
    virtual QSize maximumSize() const = 0;
    virtual Qt::Orientations expandingDirections() const = 0;
    virtual void setGeometry(const QRect &) = 0;
    virtual QRect geometry() const = 0;
    virtual bool isEmpty() const = 0;
    virtual bool hasHeightForWidth() const;
    virtual int heightForWidth(int) const;
    virtual int minimumHeightForWidth(int) const;
    virtual void invalidate();
    virtual QWidget *widget();
    virtual QLayout *layout();
    virtual QSpacerItem *spacerItem();
    Qt::Alignment alignment() const;
    void setAlignment(Qt::Alignment a);
    virtual QSizePolicy::ControlTypes controlTypes() const;
};

class QSpacerItem : public QLayoutItem
{
%TypeHeaderCode
#include <qlayoutitem.h>
%End

public:
    QSpacerItem(int w, int h, QSizePolicy::Policy hPolicy = QSizePolicy::Minimum, QSizePolicy::Policy vPolicy = QSizePolicy::Minimum);
    virtual ~QSpacerItem();
    void changeSize(int w, int h, QSizePolicy::Policy hPolicy = QSizePolicy::Minimum, QSizePolicy::Policy vPolicy = QSizePolicy::Minimum);
    virtual QSize sizeHint() const;
    virtual QSize minimumSize() const;
    virtual QSize maximumSize() const;
    virtual Qt::Orientations expandingDirections() const;
    virtual bool isEmpty() const;
    virtual void setGeometry(const QRect &);
    virtual QRect geometry() const;
    virtual QSpacerItem *spacerItem();
%If (Qt_5_5_0 -)
    QSizePolicy sizePolicy() const;
%End
};

class QWidgetItem : public QLayoutItem
{
%TypeHeaderCode
#include <qlayoutitem.h>
%End

public:
    explicit QWidgetItem(QWidget *w);
    virtual ~QWidgetItem();
    virtual QSize sizeHint() const;
    virtual QSize minimumSize() const;
    virtual QSize maximumSize() const;
    virtual Qt::Orientations expandingDirections() const;
    virtual bool isEmpty() const;
    virtual void setGeometry(const QRect &);
    virtual QRect geometry() const;
    virtual QWidget *widget();
    virtual bool hasHeightForWidth() const;
    virtual int heightForWidth(int) const;
    virtual QSizePolicy::ControlTypes controlTypes() const;

private:
    QWidgetItem(const QWidgetItem &);
};
