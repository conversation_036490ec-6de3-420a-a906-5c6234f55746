"""
Clear and Readable Dashboard for SFF Production System
Large fonts, clear text, and big buttons
"""

import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QMessageBox, QMenuBar, QAction,
    QScrollArea
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ClearModuleCard(QFrame):
    """Clear module card with large text"""
    def __init__(self, title, description, icon="📋"):
        super().__init__()
        self.title = title
        self.init_ui(title, description, icon)
    
    def init_ui(self, title, description, icon):
        """Initialize clear card UI"""
        self.setObjectName("moduleCard")
        self.setFixedSize(350, 180)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 20, 25, 20)
        layout.setSpacing(15)
        
        # Icon and title
        header_layout = QHBoxLayout()
        header_layout.setSpacing(15)
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("cardIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setObjectName("cardDescription")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Button
        button = QPushButton("OPEN")
        button.setObjectName("cardButton")
        button.clicked.connect(lambda: self.open_module(title))
        layout.addWidget(button)
    
    def open_module(self, module_name):
        """Open module"""
        parent = self.parent()
        while parent and not hasattr(parent, 'open_module'):
            parent = parent.parent()
        if parent:
            parent.open_module(module_name)

class ClearDashboard(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize clear dashboard interface"""
        self.setWindowTitle("SFF Production System - Main Dashboard")
        self.setGeometry(50, 50, 1400, 900)
        
        # Apply clear design with large fonts
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QWidget {
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #headerFrame {
                background-color: #228B22;
                border: none;
                padding: 30px;
            }
            #headerTitle {
                font-size: 36px;
                font-weight: bold;
                color: white;
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #headerSubtitle {
                font-size: 20px;
                color: white;
                font-family: 'Arial Black', 'Arial', sans-serif;
                margin-top: 10px;
            }
            #welcomeSection {
                background-color: white;
                border: 3px solid #228B22;
                border-radius: 10px;
                padding: 30px;
                margin: 25px;
            }
            #welcomeTitle {
                font-size: 28px;
                font-weight: bold;
                color: #228B22;
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #welcomeText {
                font-size: 18px;
                color: #333;
                font-family: 'Arial', sans-serif;
                margin-top: 15px;
                font-weight: bold;
            }
            #modulesSection {
                background-color: white;
                border: 3px solid #228B22;
                border-radius: 10px;
                margin: 25px;
                padding: 30px;
            }
            #sectionTitle {
                font-size: 32px;
                font-weight: bold;
                color: #228B22;
                font-family: 'Arial Black', 'Arial', sans-serif;
                margin-bottom: 25px;
            }
            #moduleCard {
                background-color: #f8fff8;
                border: 4px solid #90EE90;
                border-radius: 10px;
                margin: 10px;
            }
            #moduleCard:hover {
                border-color: #228B22;
                background-color: #f0fff0;
            }
            #cardIcon {
                font-size: 32px;
                color: #228B22;
            }
            #cardTitle {
                font-size: 18px;
                font-weight: bold;
                color: #228B22;
                font-family: 'Arial Black', 'Arial', sans-serif;
            }
            #cardDescription {
                font-size: 14px;
                color: #333;
                font-family: 'Arial', sans-serif;
                line-height: 1.5;
                font-weight: bold;
            }
            #cardButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Arial Black', 'Arial', sans-serif;
                min-height: 20px;
            }
            #cardButton:hover {
                background-color: #32CD32;
            }
            #statusBar {
                background-color: white;
                border-top: 3px solid #228B22;
                color: #228B22;
                font-family: 'Arial Black', 'Arial', sans-serif;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
            }
            QMenuBar {
                background-color: #228B22;
                color: white;
                border: none;
                font-family: 'Arial Black', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 10px 15px;
                color: white;
            }
            QMenuBar::item:selected {
                background-color: #32CD32;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Center window
        self.center_window()
        
        # Create main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # Main layout
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header section
        self.create_header(main_layout)
        
        # Content area with scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 30)
        content_layout.setSpacing(0)
        
        # Welcome section
        self.create_welcome_section(content_layout)
        
        # Modules section
        self.create_modules_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # Status bar
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('FILE')
        
        logout_action = QAction('LOGOUT', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('EXIT', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('TOOLS')
        
        settings_action = QAction('SYSTEM SETTINGS', self)
        settings_action.triggered.connect(lambda: self.open_module('System Settings'))
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('HELP')
        
        about_action = QAction('ABOUT', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_header(self, layout):
        """Create header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Left side - title
        title_layout = QVBoxLayout()
        title_layout.setSpacing(10)
        
        title = QLabel("SFF PRODUCTION SYSTEM")
        title.setObjectName("headerTitle")
        title_layout.addWidget(title)
        
        subtitle = QLabel("MAIN DASHBOARD - ADMINISTRATOR")
        subtitle.setObjectName("headerSubtitle")
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Right side - user info
        user_info = QLabel("WELCOME ADMIN")
        user_info.setStyleSheet("color: white; font-size: 24px; font-weight: bold; font-family: 'Arial Black', 'Arial', sans-serif;")
        header_layout.addWidget(user_info)
        
        layout.addWidget(header_frame)
    
    def create_welcome_section(self, layout):
        """Create welcome section"""
        welcome_frame = QFrame()
        welcome_frame.setObjectName("welcomeSection")
        
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setSpacing(15)
        
        welcome_title = QLabel("WELCOME TO SFF PRODUCTION SYSTEM")
        welcome_title.setObjectName("welcomeTitle")
        welcome_layout.addWidget(welcome_title)
        
        welcome_text = QLabel("MANAGE YOUR PRODUCTION AND QUALITY PROCESSES. SELECT A MODULE BELOW TO START.")
        welcome_text.setObjectName("welcomeText")
        welcome_text.setWordWrap(True)
        welcome_layout.addWidget(welcome_text)
        
        layout.addWidget(welcome_frame)
    
    def create_modules_section(self, layout):
        """Create modules section"""
        modules_frame = QFrame()
        modules_frame.setObjectName("modulesSection")
        
        modules_layout = QVBoxLayout(modules_frame)
        modules_layout.setSpacing(20)
        
        # Section title
        section_title = QLabel("SYSTEM MODULES")
        section_title.setObjectName("sectionTitle")
        modules_layout.addWidget(section_title)
        
        # Modules grid
        grid_layout = QGridLayout()
        grid_layout.setSpacing(20)
        grid_layout.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        
        # Define modules with icons
        modules = [
            ("USER MANAGEMENT", "MANAGE USERS AND PERMISSIONS", "👥"),
            ("INVENTORY MANAGEMENT", "TRACK MATERIALS AND STOCK", "📦"),
            ("RECIPE MANAGEMENT", "CREATE PRODUCTION RECIPES", "📋"),
            ("SAMPLE BATCHES", "TEST PRODUCTION BATCHES", "🧪"),
            ("PRODUCTION MANAGEMENT", "MANAGE PRODUCTION ORDERS", "🏭"),
            ("QUALITY ASSURANCE", "QA TESTS AND DOCUMENTS", "✅"),
            ("REPORTS", "GENERATE SYSTEM REPORTS", "📊"),
            ("SYSTEM SETTINGS", "CONFIGURE SYSTEM", "⚙️")
        ]
        
        # Create module cards in grid (3 per row)
        row, col = 0, 0
        for title, description, icon in modules:
            card = ClearModuleCard(title, description, icon)
            grid_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 3:  # 3 cards per row
                col = 0
                row += 1
        
        modules_layout.addLayout(grid_layout)
        layout.addWidget(modules_frame)
    
    def create_status_bar(self):
        """Create status bar"""
        status_bar = self.statusBar()
        status_bar.setObjectName("statusBar")
        status_bar.showMessage("SYSTEM STATUS: ONLINE | DATABASE: CONNECTED | USER: ADMINISTRATOR")
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def open_module(self, module_name):
        """Open specific module"""
        QMessageBox.information(
            self,
            "MODULE",
            f"OPENING {module_name.upper()}...\n\n"
            f"THIS MODULE WILL BE AVAILABLE SOON.\n\n"
            f"FEATURES COMING IN NEXT UPDATE."
        )
    
    def logout(self):
        """Logout and return to login"""
        reply = QMessageBox.question(
            self,
            "LOGOUT",
            "ARE YOU SURE YOU WANT TO LOGOUT?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            
            # Show login window again
            try:
                from gui.clear_login import ClearLoginWindow
                self.login_window = ClearLoginWindow()
                self.login_window.show()
            except Exception as e:
                QMessageBox.critical(self, "ERROR", f"FAILED TO OPEN LOGIN: {str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "ABOUT SFF SYSTEM",
            "SFF PRODUCTION & QUALITY MANAGEMENT SYSTEM\n"
            "VERSION 1.0.0\n\n"
            "PROFESSIONAL MANUFACTURING MANAGEMENT\n\n"
            "FEATURES:\n"
            "• USER MANAGEMENT\n"
            "• INVENTORY CONTROL\n"
            "• RECIPE MANAGEMENT\n"
            "• PRODUCTION PLANNING\n"
            "• QUALITY ASSURANCE\n"
            "• SYSTEM REPORTS\n\n"
            "© 2024 SFF PRODUCTION SYSTEMS"
        )

def main():
    """Run the clear dashboard"""
    app = QApplication(sys.argv)
    
    dashboard = ClearDashboard()
    dashboard.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
