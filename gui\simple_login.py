"""
نافذة تسجيل دخول مبسطة وفعالة
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, 
    QLabel, QLineEdit, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the login interface"""
        self.setWindowTitle("SFF Production & Quality Management System - Login")
        self.setFixedSize(400, 300)

        # Apply professional green and white design
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QLabel {
                color: #2d5a2d;
                font-size: 14px;
                font-family: Arial, sans-serif;
            }
            #title {
                font-size: 22px;
                font-weight: bold;
                color: #228B22;
                margin: 15px;
            }
            #subtitle {
                font-size: 12px;
                color: #666;
                margin-bottom: 25px;
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #90EE90;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
                margin: 3px;
                font-family: Arial, sans-serif;
            }
            QLineEdit:focus {
                border-color: #228B22;
                background-color: #f8fff8;
            }
            QPushButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                margin: 8px;
                font-family: Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #32CD32;
            }
            QPushButton:pressed {
                background-color: #006400;
            }
            #status {
                color: #dc3545;
                font-weight: bold;
                margin: 8px;
                font-size: 12px;
            }
            #info {
                color: #888;
                font-size: 10px;
                margin-top: 15px;
            }
        """)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(40, 30, 40, 30)
        
        # Title
        title = QLabel("SFF Production System")
        title.setAlignment(Qt.AlignCenter)
        title.setObjectName("title")
        layout.addWidget(title)

        subtitle = QLabel("Production & Quality Management System")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setObjectName("subtitle")
        layout.addWidget(subtitle)

        # Username field
        username_label = QLabel("Username:")
        layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter username")
        self.username_input.setText("admin")
        layout.addWidget(self.username_input)

        # Password field
        password_label = QLabel("Password:")
        layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter password")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_input)

        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.clicked.connect(self.login_clicked)
        layout.addWidget(self.login_button)

        # Status message
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setObjectName("status")
        layout.addWidget(self.status_label)

        # Info
        info_label = QLabel("Default: admin / admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setObjectName("info")
        layout.addWidget(info_label)
        
        # تعيين التركيز
        self.username_input.setFocus()
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login_clicked(self):
        """معالجة النقر على زر تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # التحقق من البيانات
        if not username:
            self.show_status("يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # Validate credentials
        if username == "admin" and password == "admin123":
            self.show_status("Login successful!", "success")

            # Show success message
            QMessageBox.information(
                self,
                "Login Successful",
                f"Welcome to SFF Production & Quality Management System!\n\n"
                f"User: {username}\n"
                f"Role: System Administrator\n\n"
                f"Opening main dashboard..."
            )

            # Open dashboard
            self.open_dashboard()
        else:
            self.show_status("Invalid username or password")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, status_type="error"):
        """إظهار رسالة الحالة"""
        self.status_label.setText(message)
        
        if status_type == "success":
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
    
    def open_dashboard(self):
        """Open main dashboard"""
        try:
            from gui.simple_dashboard import SimpleDashboard
            self.dashboard = SimpleDashboard()
            self.dashboard.show()
            self.close()
        except Exception as e:
            # If dashboard is not available, show message
            QMessageBox.information(
                self,
                "Login Successful",
                f"Login successful!\n\n"
                f"System is ready for use.\n"
                f"Dashboard will be available soon.\n\n"
                f"Error: {str(e)}"
            )

def main():
    """Run the login window"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")
    
    window = SimpleLoginWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
