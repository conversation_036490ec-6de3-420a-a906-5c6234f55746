"""
نافذة تسجيل دخول مبسطة وفعالة
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, 
    QLabel, QLineEdit, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """إنشاء واجهة تسجيل الدخول"""
        self.setWindowTitle("SFF Production & Quality Management System")
        self.setFixedSize(450, 350)
        
        # تطبيق تصميم بسيط
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #333;
                font-size: 14px;
            }
            #title {
                font-size: 20px;
                font-weight: bold;
                color: #2E86AB;
                margin: 20px;
            }
            QLineEdit {
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
                margin: 5px;
            }
            QLineEdit:focus {
                border-color: #2E86AB;
                background-color: #ffffff;
            }
            QPushButton {
                background-color: #2E86AB;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1e5f7a;
            }
            QPushButton:pressed {
                background-color: #164a5e;
            }
            #status {
                color: #dc3545;
                font-weight: bold;
                margin: 10px;
            }
        """)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(40, 30, 40, 30)
        
        # العنوان
        title = QLabel("نظام إدارة الإنتاج والجودة")
        title.setAlignment(Qt.AlignCenter)
        title.setObjectName("title")
        layout.addWidget(title)
        
        subtitle = QLabel("SFF Production & Quality Management")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 20px;")
        layout.addWidget(subtitle)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")
        layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_input)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.clicked.connect(self.login_clicked)
        layout.addWidget(self.login_button)
        
        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setObjectName("status")
        layout.addWidget(self.status_label)
        
        # معلومات إضافية
        info_label = QLabel("المستخدم الافتراضي: admin | كلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #888; font-size: 11px; margin-top: 20px;")
        layout.addWidget(info_label)
        
        # تعيين التركيز
        self.username_input.setFocus()
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login_clicked(self):
        """معالجة النقر على زر تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # التحقق من البيانات
        if not username:
            self.show_status("يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # التحقق من صحة البيانات
        if username == "admin" and password == "admin123":
            self.show_status("تم تسجيل الدخول بنجاح!", "success")
            
            # إظهار رسالة نجاح
            QMessageBox.information(
                self, 
                "نجح تسجيل الدخول", 
                f"مرحباً بك في نظام إدارة الإنتاج والجودة!\n\n"
                f"المستخدم: {username}\n"
                f"الدور: مدير النظام\n\n"
                f"سيتم فتح لوحة التحكم الرئيسية..."
            )
            
            # فتح لوحة التحكم
            self.open_dashboard()
        else:
            self.show_status("اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, status_type="error"):
        """إظهار رسالة الحالة"""
        self.status_label.setText(message)
        
        if status_type == "success":
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
    
    def open_dashboard(self):
        """فتح لوحة التحكم الرئيسية"""
        try:
            from gui.simple_dashboard import SimpleDashboard
            self.dashboard = SimpleDashboard()
            self.dashboard.show()
            self.close()
        except ImportError:
            # إذا لم تكن لوحة التحكم متوفرة، أظهر رسالة
            QMessageBox.information(
                self,
                "نجح تسجيل الدخول",
                "تم تسجيل الدخول بنجاح!\n\n"
                "النظام جاهز للاستخدام.\n"
                "لوحة التحكم ستكون متاحة قريباً."
            )

def main():
    """تشغيل نافذة تسجيل الدخول"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")
    
    window = SimpleLoginWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
