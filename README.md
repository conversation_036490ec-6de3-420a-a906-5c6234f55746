# SFF Production & Quality Management System

A comprehensive desktop application for production and quality management in manufacturing operations, built with Python and PyQt5.

## 🚀 Features

### Core Modules
- **User Management** - Role-based access control with secure authentication
- **Inventory Management** - Track materials, stock levels, and automatic low-stock alerts
- **Recipe Management** - Create and manage production formulas with cost calculations
- **Sample Batch Testing** - Test production batches before full-scale manufacturing
- **Production Management** - Manage production orders with real-time inventory checking
- **Quality Assurance** - Lab testing, document generation (MSDS, COA, TDS, Spec Sheets)
- **Comprehensive Reporting** - Production reports, analytics, and audit trails

### Security & Compliance
- ✅ Offline desktop application (no internet dependency)
- ✅ Microsoft Access database with encryption
- ✅ Role-based permissions (Admin, Production, Quality, Warehouse, Viewer)
- ✅ Complete audit trail of all user actions
- ✅ Session management with automatic timeout
- ✅ Password strength validation and account lockout protection

### Quality Documents
- **MSDS** - Material Safety Data Sheets
- **COA** - Certificate of Analysis
- **TDS** - Technical Data Sheets
- **Product Specification Sheets** with HACCP checkpoints

## 📋 Requirements

### System Requirements
- Windows 10/11 (64-bit)
- Python 3.8 or higher
- Microsoft Access Database Engine 2016 Redistributable
- Minimum 4GB RAM
- 500MB free disk space

### Python Dependencies
```
PyQt5==5.15.9
pyodbc==4.0.39
pandas==2.0.3
openpyxl==3.1.2
reportlab==4.0.4
Pillow==10.0.0
bcrypt==4.0.1
cryptography==41.0.3
python-dateutil==2.8.2
matplotlib==3.7.2
numpy==1.24.3
xlsxwriter==3.1.2
```

## 🛠️ Installation

### Step 1: Install Microsoft Access Database Engine
Download and install the Microsoft Access Database Engine 2016 Redistributable:
- [Download from Microsoft](https://www.microsoft.com/en-us/download/details.aspx?id=54920)
- Choose the version that matches your system (32-bit or 64-bit)

### Step 2: Install Python Dependencies
```bash
# Clone or download the project
cd SFF

# Install required packages
pip install -r requirements.txt
```

### Step 3: Initialize Database
The application will automatically create the database on first run, but you can also initialize it manually:
```bash
python database/create_database.py
```

## 🚀 Usage

### Starting the Application
```bash
python main.py
```

### Default Login Credentials
- **Username:** `admin`
- **Password:** `admin123`

⚠️ **Important:** Change the default password immediately after first login!

### User Roles and Permissions

| Role | Permissions |
|------|-------------|
| **Administrator** | Full system access, user management, all modules |
| **Production Staff** | Inventory (read), Recipes (read), Production (all), Sample batches (all) |
| **Quality Manager** | Quality (all), Production (read), Recipes (read), Reports (read) |
| **Warehouse Manager** | Inventory (all), Recipes (read), Production (read) |
| **Viewer** | Read-only access to all modules except user management |

## 📁 Project Structure

```
SFF/
├── main.py                     # Application entry point
├── requirements.txt            # Python dependencies
├── config/
│   └── settings.py            # Application configuration
├── database/
│   ├── create_database.py     # Database schema creation
│   ├── db_manager.py          # Database operations
│   └── sff_production_db.accdb # Access database (created automatically)
├── auth/
│   └── authentication.py     # Authentication and security
├── models/
│   └── data_models.py         # Data structure definitions
├── gui/
│   ├── login_window.py        # Login interface
│   ├── main_dashboard.py      # Main dashboard
│   ├── inventory_management.py # Inventory module
│   ├── user_management.py     # User management (to be implemented)
│   ├── recipe_management.py   # Recipe module (to be implemented)
│   ├── sample_batch.py        # Sample batch module (to be implemented)
│   ├── production_management.py # Production module (to be implemented)
│   ├── quality_assurance.py  # QA module (to be implemented)
│   └── reporting.py           # Reports module (to be implemented)
├── utils/
│   ├── validators.py          # Data validation utilities
│   ├── pdf_generator.py       # PDF document generation
│   └── excel_exporter.py      # Excel export functionality (to be implemented)
└── reports/                   # Generated reports directory
```

## 🔧 Configuration

### Database Configuration
Edit `config/settings.py` to modify database settings:
```python
DATABASE_NAME = "sff_production_db.accdb"
DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "database", DATABASE_NAME)
```

### Security Settings
```python
PASSWORD_MIN_LENGTH = 8
SESSION_TIMEOUT = 3600  # 1 hour
MAX_LOGIN_ATTEMPTS = 3
LOCKOUT_DURATION = 900  # 15 minutes
```

### GUI Settings
```python
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
THEME_COLOR = "#2E86AB"
```

## 🔒 Security Features

### Authentication
- Secure password hashing using bcrypt
- Account lockout after failed login attempts
- Session timeout management
- Password strength validation

### Data Protection
- All data stored in encrypted Access database
- No external file generation for sensitive data
- Role-based access control
- Complete audit trail

### Compliance
- HACCP checkpoint integration
- Quality document generation
- Traceability throughout production process
- Regulatory compliance reporting

## 📊 Database Schema

### Core Tables
- **Users** - User accounts and authentication
- **Materials** - Raw materials and ingredients inventory
- **Recipes** - Production formulas and ingredients
- **ProductionBatches** - Production orders and batches
- **QualityTests** - Lab test results and specifications
- **QualityDocuments** - Generated quality documents
- **InventoryMovements** - Material movement tracking
- **AuditLog** - Complete audit trail

## 🐛 Troubleshooting

### Common Issues

**Database Connection Error**
- Ensure Microsoft Access Database Engine is installed
- Check file permissions in the application directory
- Verify the database file is not corrupted

**Login Issues**
- Use default credentials: admin/admin123
- Check if account is locked (wait 15 minutes or contact admin)
- Ensure caps lock is off

**Permission Denied**
- Contact your administrator to verify your user role
- Some features require specific permissions

**Application Won't Start**
- Check Python version (3.8+ required)
- Verify all dependencies are installed: `pip install -r requirements.txt`
- Check Windows compatibility (Windows 10/11 required)

## 📞 Support

For technical support or feature requests:
- Check the troubleshooting section above
- Review the application logs in the temp directory
- Contact your system administrator

## 📄 License

This software is proprietary and confidential. Unauthorized copying, distribution, or modification is strictly prohibited.

## 🔄 Version History

### Version 1.0.0 (Current)
- Initial release
- Core authentication and user management
- Basic inventory management interface
- Database schema and security implementation
- PDF document generation framework
- Main dashboard with role-based access

### Planned Features (Next Releases)
- Complete inventory management (add/edit/delete materials)
- Recipe management with ingredient calculations
- Sample batch testing workflow
- Production order management
- Quality assurance testing interface
- Comprehensive reporting and analytics
- Data import/export functionality
- Advanced user management features

---

**SFF Production & Quality Management System** - Ensuring quality and compliance in every batch.
