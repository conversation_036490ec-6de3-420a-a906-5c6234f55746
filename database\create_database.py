"""
SFF Production & Quality Management System
Database Schema Creation for Microsoft Access
"""

import pyodbc
import os

# مسار قاعدة البيانات
DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "SFF_Production_Database.accdb")

def create_database_schema():
    """Create the complete database schema for SFF Production & Quality Management System"""
    
    # Connection string for Access database
    conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={DATABASE_PATH};'
    
    try:
        # Create database file if it doesn't exist
        if not os.path.exists(DATABASE_PATH):
            # Create empty database
            conn = pyodbc.connect(conn_str)
            conn.close()
        
        # Connect to database
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Create Users table
        cursor.execute('''
            CREATE TABLE Users (
                UserID AUTOINCREMENT PRIMARY KEY,
                Username VARCHAR(50) NOT NULL,
                PasswordHash VARCHAR(255) NOT NULL,
                Email VARCHAR(100),
                FullName VARCHAR(100) NOT NULL,
                Role VARCHAR(20) NOT NULL,
                IsActive YESNO,
                CreatedDate DATETIME,
                LastLogin DATETIME,
                FailedLoginAttempts INTEGER,
                LockedUntil DATETIME
            )
        ''')
        
        # Create Materials table
        cursor.execute('''
            CREATE TABLE Materials (
                MaterialID AUTOINCREMENT PRIMARY KEY,
                MaterialCode VARCHAR(20) NOT NULL,
                MaterialName VARCHAR(100) NOT NULL,
                MaterialType VARCHAR(20) NOT NULL,
                UnitOfMeasure VARCHAR(10) NOT NULL,
                CurrentStock DOUBLE,
                MinimumThreshold DOUBLE,
                UnitCost CURRENCY,
                Supplier VARCHAR(100),
                StorageLocation VARCHAR(50),
                ExpiryDate DATETIME,
                CreatedDate DATETIME,
                IsActive YESNO
            )
        ''')
        
        # Create Recipes table
        cursor.execute('''
            CREATE TABLE Recipes (
                RecipeID AUTOINCREMENT PRIMARY KEY,
                RecipeCode VARCHAR(20) NOT NULL UNIQUE,
                RecipeName VARCHAR(100) NOT NULL,
                Version VARCHAR(10) DEFAULT '1.0',
                Description MEMO,
                BatchSize DECIMAL(10,3) NOT NULL,
                UnitOfMeasure VARCHAR(10) NOT NULL,
                TotalCost CURRENCY DEFAULT 0,
                IsApproved YESNO DEFAULT False,
                CreatedBy INTEGER NOT NULL,
                CreatedDate DATETIME DEFAULT Now(),
                ApprovedBy INTEGER,
                ApprovedDate DATETIME,
                IsActive YESNO DEFAULT True,
                FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
                FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID)
            )
        ''')
        
        # Create RecipeIngredients table
        cursor.execute('''
            CREATE TABLE RecipeIngredients (
                RecipeIngredientID AUTOINCREMENT PRIMARY KEY,
                RecipeID INTEGER NOT NULL,
                MaterialID INTEGER NOT NULL,
                Quantity DECIMAL(10,3) NOT NULL,
                UnitOfMeasure VARCHAR(10) NOT NULL,
                Percentage DECIMAL(5,2),
                Notes MEMO,
                FOREIGN KEY (RecipeID) REFERENCES Recipes(RecipeID),
                FOREIGN KEY (MaterialID) REFERENCES Materials(MaterialID)
            )
        ''')
        
        # Create ProductionBatches table
        cursor.execute('''
            CREATE TABLE ProductionBatches (
                BatchID AUTOINCREMENT PRIMARY KEY,
                BatchNumber VARCHAR(30) NOT NULL UNIQUE,
                RecipeID INTEGER NOT NULL,
                BatchType VARCHAR(20) DEFAULT 'PRODUCTION',
                PlannedQuantity DECIMAL(10,3) NOT NULL,
                ActualQuantity DECIMAL(10,3) DEFAULT 0,
                Status VARCHAR(20) DEFAULT 'PLANNED',
                OperatorID INTEGER NOT NULL,
                StartDate DATETIME,
                EndDate DATETIME,
                TotalCost CURRENCY DEFAULT 0,
                Notes MEMO,
                CreatedDate DATETIME DEFAULT Now(),
                FOREIGN KEY (RecipeID) REFERENCES Recipes(RecipeID),
                FOREIGN KEY (OperatorID) REFERENCES Users(UserID)
            )
        ''')
        
        # Create BatchIngredientUsage table
        cursor.execute('''
            CREATE TABLE BatchIngredientUsage (
                UsageID AUTOINCREMENT PRIMARY KEY,
                BatchID INTEGER NOT NULL,
                MaterialID INTEGER NOT NULL,
                PlannedQuantity DECIMAL(10,3) NOT NULL,
                ActualQuantity DECIMAL(10,3) DEFAULT 0,
                UnitCost CURRENCY NOT NULL,
                TotalCost CURRENCY DEFAULT 0,
                FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID),
                FOREIGN KEY (MaterialID) REFERENCES Materials(MaterialID)
            )
        ''')
        
        # Create QualityTests table
        cursor.execute('''
            CREATE TABLE QualityTests (
                TestID AUTOINCREMENT PRIMARY KEY,
                BatchID INTEGER NOT NULL,
                TestType VARCHAR(50) NOT NULL,
                TestParameter VARCHAR(100) NOT NULL,
                Specification VARCHAR(100),
                Result VARCHAR(100),
                Status VARCHAR(20) DEFAULT 'PENDING',
                TestedBy INTEGER NOT NULL,
                TestDate DATETIME DEFAULT Now(),
                Notes MEMO,
                FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID),
                FOREIGN KEY (TestedBy) REFERENCES Users(UserID)
            )
        ''')
        
        # Create QualityDocuments table
        cursor.execute('''
            CREATE TABLE QualityDocuments (
                DocumentID AUTOINCREMENT PRIMARY KEY,
                BatchID INTEGER NOT NULL,
                DocumentType VARCHAR(20) NOT NULL,
                DocumentContent MEMO,
                GeneratedBy INTEGER NOT NULL,
                GeneratedDate DATETIME DEFAULT Now(),
                IsApproved YESNO DEFAULT False,
                ApprovedBy INTEGER,
                ApprovedDate DATETIME,
                FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID),
                FOREIGN KEY (GeneratedBy) REFERENCES Users(UserID),
                FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID)
            )
        ''')
        
        # Create InventoryMovements table
        cursor.execute('''
            CREATE TABLE InventoryMovements (
                MovementID AUTOINCREMENT PRIMARY KEY,
                MaterialID INTEGER NOT NULL,
                MovementType VARCHAR(20) NOT NULL,
                Quantity DECIMAL(10,3) NOT NULL,
                ReferenceID INTEGER,
                ReferenceType VARCHAR(20),
                PerformedBy INTEGER NOT NULL,
                MovementDate DATETIME DEFAULT Now(),
                Notes MEMO,
                FOREIGN KEY (MaterialID) REFERENCES Materials(MaterialID),
                FOREIGN KEY (PerformedBy) REFERENCES Users(UserID)
            )
        ''')
        
        # Create AuditLog table
        cursor.execute('''
            CREATE TABLE AuditLog (
                LogID AUTOINCREMENT PRIMARY KEY,
                UserID INTEGER NOT NULL,
                Action VARCHAR(50) NOT NULL,
                TableName VARCHAR(50) NOT NULL,
                RecordID INTEGER,
                OldValues MEMO,
                NewValues MEMO,
                Timestamp DATETIME DEFAULT Now(),
                IPAddress VARCHAR(15),
                FOREIGN KEY (UserID) REFERENCES Users(UserID)
            )
        ''')
        
        conn.commit()
        print("Database schema created successfully!")
        
        # Create default admin user
        create_default_admin(cursor)
        conn.commit()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error creating database schema: {str(e)}")
        return False

def create_default_admin(cursor):
    """Create default administrator user"""
    import bcrypt
    
    # Hash default password
    password = "admin123"
    password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    cursor.execute('''
        INSERT INTO Users (Username, PasswordHash, Email, FullName, Role, IsActive)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', ('admin', password_hash, '<EMAIL>', 'System Administrator', 'ADMIN', True))
    
    print("Default admin user created (username: admin, password: admin123)")

if __name__ == "__main__":
    create_database_schema()
