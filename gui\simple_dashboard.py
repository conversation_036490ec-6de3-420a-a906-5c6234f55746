"""
Simple Dashboard for SFF Production System
"""

import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QMessageBox, QMenuBar, QAction
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleDashboard(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the dashboard interface"""
        self.setWindowTitle("SFF Production & Quality Management System - Dashboard")
        self.setGeometry(100, 100, 900, 600)
        
        # Apply green and white theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QLabel {
                color: #2d5a2d;
                font-family: Arial, sans-serif;
            }
            #headerTitle {
                font-size: 24px;
                font-weight: bold;
                color: #228B22;
                margin: 15px;
            }
            #welcomeLabel {
                font-size: 16px;
                color: #2d5a2d;
                margin: 10px;
            }
            #moduleCard {
                background-color: #f8fff8;
                border: 2px solid #90EE90;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
            #moduleCard:hover {
                border-color: #228B22;
                background-color: #f0fff0;
            }
            #cardTitle {
                font-size: 16px;
                font-weight: bold;
                color: #228B22;
                margin-bottom: 8px;
            }
            #cardDescription {
                font-size: 12px;
                color: #666;
                margin-bottom: 10px;
            }
            QPushButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                font-family: Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #32CD32;
            }
            QPushButton:pressed {
                background-color: #006400;
            }
            #statusBar {
                background-color: #f8fff8;
                border-top: 1px solid #90EE90;
                color: #2d5a2d;
            }
            QMenuBar {
                background-color: #228B22;
                color: white;
                border: none;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 4px 8px;
                color: white;
            }
            QMenuBar::item:selected {
                background-color: #32CD32;
            }
        """)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Center window
        self.center_window()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Header
        header_layout = QVBoxLayout()
        
        title = QLabel("SFF Production & Quality Management System")
        title.setAlignment(Qt.AlignCenter)
        title.setObjectName("headerTitle")
        header_layout.addWidget(title)
        
        welcome = QLabel("Welcome, Administrator")
        welcome.setAlignment(Qt.AlignCenter)
        welcome.setObjectName("welcomeLabel")
        header_layout.addWidget(welcome)
        
        main_layout.addLayout(header_layout)
        
        # Modules grid
        modules_layout = QGridLayout()
        modules_layout.setSpacing(15)
        
        # Define modules
        modules = [
            ("User Management", "Manage users and permissions", "admin_only"),
            ("Inventory Management", "Track materials and stock levels", "all"),
            ("Recipe Management", "Create and manage production recipes", "all"),
            ("Sample Batches", "Test production batches", "production"),
            ("Production Management", "Manage production orders", "production"),
            ("Quality Assurance", "QA tests and documents", "quality"),
            ("Reports", "Generate system reports", "all"),
            ("System Settings", "Configure system settings", "admin_only")
        ]
        
        # Create module cards
        row, col = 0, 0
        for title, description, access in modules:
            card = self.create_module_card(title, description, access)
            modules_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 3:  # 3 cards per row
                col = 0
                row += 1
        
        main_layout.addLayout(modules_layout)
        
        # Status section
        status_layout = QHBoxLayout()
        
        status_info = QLabel("System Status: Online | Database: Connected | User: Administrator")
        status_info.setStyleSheet("color: #228B22; font-size: 12px; font-weight: bold;")
        status_layout.addWidget(status_info)
        
        status_layout.addStretch()
        
        logout_btn = QPushButton("Logout")
        logout_btn.clicked.connect(self.logout)
        status_layout.addWidget(logout_btn)
        
        main_layout.addLayout(status_layout)
        
        # Add stretch to push everything up
        main_layout.addStretch()
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        logout_action = QAction('Logout', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_module_card(self, title, description, access):
        """Create a module card"""
        card = QFrame()
        card.setObjectName("moduleCard")
        card.setFixedSize(250, 120)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # Title
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setObjectName("cardDescription")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Button
        button = QPushButton("Open Module")
        button.clicked.connect(lambda: self.open_module(title))
        layout.addWidget(button)
        
        return card
    
    def center_window(self):
        """Center the window on screen"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def open_module(self, module_name):
        """Open specific module"""
        if module_name == "Inventory Management":
            try:
                from gui.inventory_management import InventoryManagement
                # Create a simple auth manager for demo
                class DemoAuth:
                    def has_permission(self, perm):
                        return True
                
                self.inventory_window = InventoryManagement(DemoAuth())
                self.inventory_window.show()
            except Exception as e:
                QMessageBox.information(
                    self,
                    "Module",
                    f"Opening {module_name}...\n\nInventory module will be available soon.\n\nError: {str(e)}"
                )
        else:
            QMessageBox.information(
                self,
                "Module",
                f"Opening {module_name}...\n\nThis module will be available in the next update."
            )
    
    def logout(self):
        """Logout and return to login"""
        reply = QMessageBox.question(
            self,
            "Logout",
            "Are you sure you want to logout?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            
            # Show login window again
            try:
                from gui.simple_login import SimpleLoginWindow
                self.login_window = SimpleLoginWindow()
                self.login_window.show()
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to open login window: {str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About SFF Production System",
            "SFF Production & Quality Management System\n"
            "Version 1.0.0\n\n"
            "A comprehensive production and quality management system\n"
            "for manufacturing operations.\n\n"
            "Features:\n"
            "• User Management\n"
            "• Inventory Management\n"
            "• Recipe Management\n"
            "• Production Planning\n"
            "• Quality Assurance\n"
            "• Comprehensive Reporting\n\n"
            "All data is securely stored in Microsoft Access database."
        )

def main():
    """Run the dashboard"""
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    
    dashboard = SimpleDashboard()
    dashboard.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
