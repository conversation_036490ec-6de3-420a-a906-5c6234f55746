"""
Ultra Clear Login Window for SFF Production System
Maximum text clarity and readability
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class UltraClearLoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize ultra clear login interface"""
        self.setWindowTitle("SFF Production & Quality Management System")
        self.setFixedSize(520, 480)
        
        # Apply ultra clear design
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QWidget {
                font-family: 'Arial', 'Helvetica', sans-serif;
            }
            #mainContainer {
                background-color: white;
                border: 3px solid #228B22;
                border-radius: 10px;
            }
            #headerSection {
                background-color: #228B22;
                border-radius: 10px 10px 0px 0px;
                padding: 30px;
            }
            #title {
                font-size: 24px;
                font-weight: bold;
                color: white;
                font-family: 'Arial', 'Helvetica', sans-serif;
            }
            #subtitle {
                font-size: 16px;
                color: white;
                font-family: 'Arial', 'Helvetica', sans-serif;
                margin-top: 10px;
            }
            #formSection {
                padding: 40px;
                background-color: white;
            }
            #fieldLabel {
                font-size: 20px;
                color: #228B22;
                font-weight: bold;
                margin-bottom: 15px;
                font-family: 'Arial', 'Helvetica', sans-serif;
                background-color: white;
                padding: 10px 0px;
                border: none;
            }
        """)
        
        # Center the window
        self.center_window()
        
        # Create main container
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(0)
        
        # Create main container frame
        container = QFrame()
        container.setObjectName("mainContainer")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # Header section
        header_section = QFrame()
        header_section.setObjectName("headerSection")
        header_layout = QVBoxLayout(header_section)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setAlignment(Qt.AlignCenter)
        
        title = QLabel("SFF Production System")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        subtitle = QLabel("Login to Continue")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle)
        
        container_layout.addWidget(header_section)
        
        # Form section
        form_section = QFrame()
        form_section.setObjectName("formSection")
        form_layout = QVBoxLayout(form_section)
        form_layout.setSpacing(10)
        
        # Username field
        username_label = QLabel("Username:")
        username_label.setObjectName("fieldLabel")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Type your username")
        self.username_input.setText("admin")
        
        # Apply ultra clear styling to username input
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 20px 25px;
                border: 5px solid #90EE90;
                border-radius: 10px;
                font-size: 24px;
                background-color: #ffffff;
                margin-bottom: 25px;
                font-family: 'Courier New', 'Arial Black', monospace;
                color: #000000;
                font-weight: 900;
                line-height: 1.5;
                text-align: left;
            }
            QLineEdit:focus {
                border-color: #228B22;
                background-color: #ffffff;
                outline: none;
                color: #000000;
                font-weight: 900;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: normal;
                font-weight: normal;
                font-size: 20px;
            }
        """)
        form_layout.addWidget(self.username_input)
        
        # Password field
        password_label = QLabel("Password:")
        password_label.setObjectName("fieldLabel")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Type your password")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        
        # Apply ultra clear styling to password input
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 20px 25px;
                border: 5px solid #90EE90;
                border-radius: 10px;
                font-size: 24px;
                background-color: #ffffff;
                margin-bottom: 25px;
                font-family: 'Courier New', 'Arial Black', monospace;
                color: #000000;
                font-weight: 900;
                line-height: 1.5;
                text-align: left;
            }
            QLineEdit:focus {
                border-color: #228B22;
                background-color: #ffffff;
                outline: none;
                color: #000000;
                font-weight: 900;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: normal;
                font-weight: normal;
                font-size: 20px;
            }
        """)
        form_layout.addWidget(self.password_input)
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 20px 35px;
                border-radius: 10px;
                font-size: 20px;
                font-weight: bold;
                margin-top: 20px;
                font-family: 'Arial', 'Helvetica', sans-serif;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #32CD32;
            }
            QPushButton:pressed {
                background-color: #006400;
            }
        """)
        self.login_button.clicked.connect(self.login_clicked)
        form_layout.addWidget(self.login_button)
        
        # Status message
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #dc3545;
                font-weight: bold;
                margin-top: 20px;
                font-size: 16px;
                font-family: 'Arial', 'Helvetica', sans-serif;
            }
        """)
        form_layout.addWidget(self.status_label)
        
        # Info label
        info_label = QLabel("Default: admin / admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 14px;
                margin-top: 25px;
                font-family: 'Arial', 'Helvetica', sans-serif;
                font-weight: normal;
            }
        """)
        form_layout.addWidget(info_label)
        
        container_layout.addWidget(form_section)
        main_layout.addWidget(container)
        
        # Set focus and connect Enter key
        self.username_input.setFocus()
        self.username_input.returnPressed.connect(self.login_clicked)
        self.password_input.returnPressed.connect(self.login_clicked)
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login_clicked(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # Clear previous status
        self.status_label.setText("")
        
        # Validate input
        if not username:
            self.show_status("Please enter your username")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("Please enter your password")
            self.password_input.setFocus()
            return
        
        # Validate credentials
        if username == "admin" and password == "admin123":
            self.show_status("Login successful!", "success")

            # Open dashboard immediately without message box
            self.open_dashboard()
        else:
            self.show_status("Invalid username or password")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, status_type="error"):
        """Show status message"""
        self.status_label.setText(message)
        
        if status_type == "success":
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #228B22;
                    font-weight: bold;
                    font-size: 16px;
                    font-family: 'Arial', 'Helvetica', sans-serif;
                }
            """)
        else:
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #dc3545;
                    font-weight: bold;
                    font-size: 16px;
                    font-family: 'Arial', 'Helvetica', sans-serif;
                }
            """)
    
    def open_dashboard(self):
        """Open main dashboard"""
        try:
            # Import and create dashboard
            from gui.perfect_dashboard import PerfectDashboard
            self.dashboard = PerfectDashboard()

            # Show dashboard first
            self.dashboard.show()

            # Then close login window
            self.close()

            # Show welcome message after dashboard is open
            QMessageBox.information(
                self.dashboard,
                "Welcome",
                f"Welcome to SFF Production System!\n\n"
                f"User: admin\n"
                f"Role: System Administrator\n\n"
                f"Dashboard is now ready for use."
            )

        except Exception as e:
            # If dashboard fails, show error but keep login open
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open dashboard:\n\n{str(e)}\n\n"
                f"Please try again or contact support."
            )

def main():
    """Run the ultra clear login window"""
    app = QApplication(sys.argv)
    app.setApplicationName("SFF Production System")
    
    window = UltraClearLoginWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
