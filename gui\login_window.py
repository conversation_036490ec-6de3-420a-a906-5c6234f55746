"""
SFF Production & Quality Management System
Login Window GUI
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame, QCheckBox,
    QProgressBar, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon
from auth.authentication import AuthenticationManager
from config.settings import APP_NAME, APP_VERSION, THEME_COLOR, ERROR_COLOR, SUCCESS_COLOR

class LoginWorker(QThread):
    """Worker thread for login authentication"""
    login_result = pyqtSignal(dict)
    
    def __init__(self, username, password):
        super().__init__()
        self.username = username
        self.password = password
        self.auth_manager = AuthenticationManager()
    
    def run(self):
        result = self.auth_manager.login(self.username, self.password)
        self.login_result.emit(result)

class LoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.auth_manager = AuthenticationManager()
        self.login_worker = None
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(f"{APP_NAME} - Login")
        self.setFixedSize(400, 500)
        self.setStyleSheet(self.get_stylesheet())
        
        # Center the window
        self.center_window()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # Add spacer at top
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Logo/Title section
        self.create_header(main_layout)
        
        # Login form
        self.create_login_form(main_layout)
        
        # Buttons
        self.create_buttons(main_layout)
        
        # Status section
        self.create_status_section(main_layout)
        
        # Add spacer at bottom
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Set focus to username field
        self.username_input.setFocus()
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def create_header(self, layout):
        """Create header with logo and title"""
        header_layout = QVBoxLayout()
        
        # Application title
        title_label = QLabel(APP_NAME)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("title")
        header_layout.addWidget(title_label)
        
        # Version
        version_label = QLabel(f"Version {APP_VERSION}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setObjectName("version")
        header_layout.addWidget(version_label)
        
        # Company
        company_label = QLabel("Production & Quality Management")
        company_label.setAlignment(Qt.AlignCenter)
        company_label.setObjectName("company")
        header_layout.addWidget(company_label)
        
        layout.addLayout(header_layout)
    
    def create_login_form(self, layout):
        """Create login form fields"""
        form_frame = QFrame()
        form_frame.setObjectName("loginForm")
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # Username field
        username_label = QLabel("Username:")
        username_label.setObjectName("fieldLabel")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setObjectName("inputField")
        self.username_input.setPlaceholderText("Enter your username")
        self.username_input.setText("admin")  # قيمة افتراضية
        self.username_input.returnPressed.connect(self.on_login_clicked)
        self.username_input.setEnabled(True)  # تأكد من التفعيل
        self.username_input.setReadOnly(False)  # تأكد من عدم القراءة فقط
        form_layout.addWidget(self.username_input)
        
        # Password field
        password_label = QLabel("Password:")
        password_label.setObjectName("fieldLabel")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setObjectName("inputField")
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setText("admin123")  # قيمة افتراضية
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.returnPressed.connect(self.on_login_clicked)
        self.password_input.setEnabled(True)  # تأكد من التفعيل
        self.password_input.setReadOnly(False)  # تأكد من عدم القراءة فقط
        form_layout.addWidget(self.password_input)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember me")
        self.remember_checkbox.setObjectName("checkbox")
        form_layout.addWidget(self.remember_checkbox)
        
        layout.addWidget(form_frame)
    
    def create_buttons(self, layout):
        """Create login and other buttons"""
        button_layout = QVBoxLayout()
        button_layout.setSpacing(10)
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.on_login_clicked)
        button_layout.addWidget(self.login_button)
        
        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)
        button_layout.addWidget(self.progress_bar)
        
        # Forgot password button
        forgot_button = QPushButton("Forgot Password?")
        forgot_button.setObjectName("linkButton")
        forgot_button.clicked.connect(self.on_forgot_password_clicked)
        button_layout.addWidget(forgot_button)
        
        layout.addLayout(button_layout)
    
    def create_status_section(self, layout):
        """Create status message area"""
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setObjectName("statusLabel")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
    
    def get_stylesheet(self):
        """Return the stylesheet for the login window"""
        return f"""
            QMainWindow {{
                background-color: #f5f5f5;
            }}
            
            #title {{
                font-size: 24px;
                font-weight: bold;
                color: {THEME_COLOR};
                margin-bottom: 5px;
            }}
            
            #version {{
                font-size: 12px;
                color: #666;
                margin-bottom: 5px;
            }}
            
            #company {{
                font-size: 14px;
                color: #888;
                margin-bottom: 20px;
            }}
            
            #loginForm {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
            }}
            
            #fieldLabel {{
                font-weight: bold;
                color: #333;
                margin-bottom: 5px;
            }}
            
            #inputField {{
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                background-color: white;
            }}
            
            #inputField:focus {{
                border-color: {THEME_COLOR};
                outline: none;
            }}
            
            #checkbox {{
                color: #666;
                font-size: 12px;
            }}
            
            #loginButton {{
                background-color: {THEME_COLOR};
                color: white;
                border: none;
                padding: 12px;
                border-radius: 4px;
                font-size: 16px;
                font-weight: bold;
            }}
            
            #loginButton:hover {{
                background-color: #1e5f7a;
            }}
            
            #loginButton:pressed {{
                background-color: #164a5e;
            }}
            
            #loginButton:disabled {{
                background-color: #ccc;
                color: #666;
            }}
            
            #linkButton {{
                background-color: transparent;
                color: {THEME_COLOR};
                border: none;
                text-decoration: underline;
                font-size: 12px;
            }}
            
            #linkButton:hover {{
                color: #1e5f7a;
            }}
            
            #progressBar {{
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
            }}
            
            #progressBar::chunk {{
                background-color: {THEME_COLOR};
                border-radius: 3px;
            }}
            
            #statusLabel {{
                font-size: 12px;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
            }}
            
            .error {{
                background-color: #ffe6e6;
                color: {ERROR_COLOR};
                border: 1px solid {ERROR_COLOR};
            }}
            
            .success {{
                background-color: #e6ffe6;
                color: {SUCCESS_COLOR};
                border: 1px solid {SUCCESS_COLOR};
            }}
            
            .warning {{
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }}
        """
    
    def on_login_clicked(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # Validate input
        if not username:
            self.show_status("Please enter your username", "error")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_status("Please enter your password", "error")
            self.password_input.setFocus()
            return
        
        # Disable form and show progress
        self.set_form_enabled(False)
        self.show_progress(True)
        self.show_status("Authenticating...", "info")
        
        # Start login worker thread
        self.login_worker = LoginWorker(username, password)
        self.login_worker.login_result.connect(self.on_login_result)
        self.login_worker.start()
    
    def on_login_result(self, result):
        """Handle login result from worker thread"""
        self.show_progress(False)
        self.set_form_enabled(True)
        
        if result['success']:
            self.show_status("Login successful! Loading application...", "success")
            # Store authentication manager with successful login
            self.auth_manager = self.login_worker.auth_manager
            
            # Close login window and open main application
            QTimer.singleShot(1000, self.open_main_application)
        else:
            self.show_status(result['message'], "error")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def open_main_application(self):
        """Open the main application window"""
        from gui.main_dashboard import MainDashboard
        
        self.main_window = MainDashboard(self.auth_manager)
        self.main_window.show()
        self.close()
    
    def on_forgot_password_clicked(self):
        """Handle forgot password button click"""
        QMessageBox.information(
            self,
            "Forgot Password",
            "Please contact your system administrator to reset your password.\n\n"
            "For security reasons, password reset must be done by an administrator."
        )
    
    def show_status(self, message, status_type="info"):
        """Show status message"""
        self.status_label.setText(message)
        
        # Remove existing status classes
        self.status_label.setProperty("class", "")
        
        # Apply new status class
        if status_type == "error":
            self.status_label.setProperty("class", "error")
        elif status_type == "success":
            self.status_label.setProperty("class", "success")
        elif status_type == "warning":
            self.status_label.setProperty("class", "warning")
        
        # Refresh style
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)
    
    def show_progress(self, show):
        """Show or hide progress bar"""
        if show:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
        else:
            self.progress_bar.setVisible(False)
    
    def set_form_enabled(self, enabled):
        """Enable or disable form controls"""
        self.username_input.setEnabled(enabled)
        self.password_input.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)

def main():
    """Main function to run the login window"""
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    
    # Create and show login window
    login_window = LoginWindow()
    login_window.show()
    
    sys.exit(app.exec_())
