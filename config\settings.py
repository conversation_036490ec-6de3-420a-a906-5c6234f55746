"""
SFF Production & Quality Management System
Configuration Settings
"""

import os

# Application Information
APP_NAME = "SFF Production & Quality Management System"
APP_VERSION = "1.0.0"
COMPANY_NAME = "SFF"

# Database Configuration
DATABASE_NAME = "SFF_Production_Database.accdb"
DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), DATABASE_NAME)

# Security Settings
PASSWORD_MIN_LENGTH = 8
SESSION_TIMEOUT = 3600  # 1 hour in seconds
MAX_LOGIN_ATTEMPTS = 3
LOCKOUT_DURATION = 900  # 15 minutes in seconds

# User Roles
USER_ROLES = {
    'ADMIN': 'Administrator',
    'PRODUCTION': 'Production Staff',
    'QUALITY': 'Quality Manager',
    'WAREHOUSE': 'Warehouse Manager',
    'VIEWER': 'Viewer'
}

# Role Permissions
ROLE_PERMISSIONS = {
    'ADMIN': ['all'],
    'PRODUCTION': ['inventory_read', 'recipe_read', 'production_all', 'sample_all'],
    'QUALITY': ['quality_all', 'production_read', 'recipe_read', 'reporting_read'],
    'WAREHOUSE': ['inventory_all', 'recipe_read', 'production_read'],
    'VIEWER': ['inventory_read', 'recipe_read', 'production_read', 'quality_read', 'reporting_read']
}

# File Paths
REPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "reports")
TEMP_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "temp")

# GUI Settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
THEME_COLOR = "#2E86AB"
SECONDARY_COLOR = "#A23B72"
SUCCESS_COLOR = "#28A745"
WARNING_COLOR = "#FFC107"
ERROR_COLOR = "#DC3545"

# Inventory Settings
LOW_STOCK_THRESHOLD = 10  # Default minimum threshold
CRITICAL_STOCK_THRESHOLD = 5

# Production Settings
BATCH_ID_PREFIX = "SFF"
SAMPLE_BATCH_PREFIX = "SAMPLE"

# Quality Settings
QA_DOCUMENT_TYPES = ['MSDS', 'COA', 'TDS', 'SPEC_SHEET']

# Report Settings
DEFAULT_REPORT_FORMAT = 'PDF'
SUPPORTED_EXPORT_FORMATS = ['PDF', 'Excel']

# Create directories if they don't exist
os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(TEMP_DIR, exist_ok=True)
