-- نظام إدارة الإنتاج والجودة SFF
-- ملف إنشاء الجداول لقاعدة البيانات
-- قم بتشغيل هذه الأوامر في Microsoft Access

-- 1. جدو<PERSON> المستخدمين
CREATE TABLE Users (
    UserID COUNTER PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    PasswordHash TEXT(255) NOT NULL,
    Email TEXT(100),
    FullName TEXT(100) NOT NULL,
    Role TEXT(20) NOT NULL,
    IsActive YESNO,
    CreatedDate DATETIME,
    LastLogin DATETIME,
    FailedLoginAttempts INTEGER,
    LockedUntil DATETIME
);

-- 2. جدول المواد الخام والمكونات
CREATE TABLE Materials (
    MaterialID COUNTER PRIMARY KEY,
    MaterialCode TEXT(20) NOT NULL,
    MaterialName TEXT(100) NOT NULL,
    MaterialType TEXT(20) NOT NULL,
    UnitOfMeasure TEXT(10) NOT NULL,
    CurrentStock DOUBLE,
    MinimumThreshold DOUBLE,
    UnitCost CURRENCY,
    Supplier TEXT(100),
    StorageLocation TEXT(50),
    ExpiryDate DATETIME,
    CreatedDate DATETIME,
    IsActive YESNO
);

-- 3. جدول الوصفات
CREATE TABLE Recipes (
    RecipeID COUNTER PRIMARY KEY,
    RecipeCode TEXT(20) NOT NULL,
    RecipeName TEXT(100) NOT NULL,
    Version TEXT(10),
    Description MEMO,
    BatchSize DOUBLE NOT NULL,
    UnitOfMeasure TEXT(10) NOT NULL,
    TotalCost CURRENCY,
    IsApproved YESNO,
    CreatedBy INTEGER NOT NULL,
    CreatedDate DATETIME,
    ApprovedBy INTEGER,
    ApprovedDate DATETIME,
    IsActive YESNO
);

-- 4. جدول مكونات الوصفات
CREATE TABLE RecipeIngredients (
    RecipeIngredientID COUNTER PRIMARY KEY,
    RecipeID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitOfMeasure TEXT(10) NOT NULL,
    Percentage DOUBLE,
    Notes MEMO
);

-- 5. جدول دفعات الإنتاج
CREATE TABLE ProductionBatches (
    BatchID COUNTER PRIMARY KEY,
    BatchNumber TEXT(30) NOT NULL,
    RecipeID INTEGER NOT NULL,
    BatchType TEXT(20),
    PlannedQuantity DOUBLE NOT NULL,
    ActualQuantity DOUBLE,
    Status TEXT(20),
    OperatorID INTEGER NOT NULL,
    StartDate DATETIME,
    EndDate DATETIME,
    TotalCost CURRENCY,
    Notes MEMO,
    CreatedDate DATETIME
);

-- 6. جدول استخدام المواد في الدفعات
CREATE TABLE BatchIngredientUsage (
    UsageID COUNTER PRIMARY KEY,
    BatchID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    PlannedQuantity DOUBLE NOT NULL,
    ActualQuantity DOUBLE,
    UnitCost CURRENCY NOT NULL,
    TotalCost CURRENCY
);

-- 7. جدول اختبارات الجودة
CREATE TABLE QualityTests (
    TestID COUNTER PRIMARY KEY,
    BatchID INTEGER NOT NULL,
    TestType TEXT(50) NOT NULL,
    TestParameter TEXT(100) NOT NULL,
    Specification TEXT(100),
    Result TEXT(100),
    Status TEXT(20),
    TestedBy INTEGER NOT NULL,
    TestDate DATETIME,
    Notes MEMO
);

-- 8. جدول وثائق الجودة
CREATE TABLE QualityDocuments (
    DocumentID COUNTER PRIMARY KEY,
    BatchID INTEGER NOT NULL,
    DocumentType TEXT(20) NOT NULL,
    DocumentContent MEMO,
    GeneratedBy INTEGER NOT NULL,
    GeneratedDate DATETIME,
    IsApproved YESNO,
    ApprovedBy INTEGER,
    ApprovedDate DATETIME
);

-- 9. جدول حركات المخزون
CREATE TABLE InventoryMovements (
    MovementID COUNTER PRIMARY KEY,
    MaterialID INTEGER NOT NULL,
    MovementType TEXT(20) NOT NULL,
    Quantity DOUBLE NOT NULL,
    ReferenceID INTEGER,
    ReferenceType TEXT(20),
    PerformedBy INTEGER NOT NULL,
    MovementDate DATETIME,
    Notes MEMO
);

-- 10. جدول سجل المراجعة
CREATE TABLE AuditLog (
    LogID COUNTER PRIMARY KEY,
    UserID INTEGER NOT NULL,
    Action TEXT(50) NOT NULL,
    TableName TEXT(50) NOT NULL,
    RecordID INTEGER,
    OldValues MEMO,
    NewValues MEMO,
    Timestamp DATETIME,
    IPAddress TEXT(15)
);

-- إدراج المستخدم الافتراضي (المدير)
-- كلمة المرور: admin123 (مشفرة)
INSERT INTO Users (Username, PasswordHash, Email, FullName, Role, IsActive, CreatedDate, FailedLoginAttempts)
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXfs2Stk5v9W', '<EMAIL>', 'مدير النظام', 'ADMIN', True, Now(), 0);

-- إدراج بيانات تجريبية للمواد
INSERT INTO Materials (MaterialCode, MaterialName, MaterialType, UnitOfMeasure, CurrentStock, MinimumThreshold, UnitCost, Supplier, StorageLocation, IsActive, CreatedDate)
VALUES 
('RAW001', 'دقيق القمح', 'RAW_INGREDIENT', 'كيلو', 1000, 50, 2.5, 'مورد المواد الخام', 'مخزن A', True, Now()),
('RAW002', 'سكر أبيض', 'RAW_INGREDIENT', 'كيلو', 500, 25, 3.0, 'مورد المواد الخام', 'مخزن A', True, Now()),
('FLV001', 'نكهة الفانيليا', 'FLAVOR', 'لتر', 20, 5, 15.0, 'مورد النكهات', 'مخزن B', True, Now()),
('CHM001', 'بيكنج بودر', 'CHEMICAL', 'كيلو', 100, 10, 8.0, 'مورد الكيماويات', 'مخزن C', True, Now()),
('PKG001', 'أكياس تعبئة', 'PACKAGING', 'قطعة', 5000, 500, 0.1, 'مورد التعبئة', 'مخزن D', True, Now());

-- إدراج وصفة تجريبية
INSERT INTO Recipes (RecipeCode, RecipeName, Description, BatchSize, UnitOfMeasure, TotalCost, IsApproved, CreatedBy, IsActive, CreatedDate, Version)
VALUES ('RCP001', 'كعكة الفانيليا الأساسية', 'وصفة أساسية لكعكة الفانيليا', 10.0, 'كيلو', 0, True, 1, True, Now(), '1.0');

-- إدراج مكونات الوصفة
INSERT INTO RecipeIngredients (RecipeID, MaterialID, Quantity, UnitOfMeasure, Percentage, Notes)
VALUES 
(1, 1, 5.0, 'كيلو', 50.0, 'المكون الأساسي'),
(1, 2, 2.0, 'كيلو', 20.0, 'للتحلية'),
(1, 3, 0.1, 'لتر', 1.0, 'للنكهة'),
(1, 4, 0.2, 'كيلو', 2.0, 'للانتفاخ');
