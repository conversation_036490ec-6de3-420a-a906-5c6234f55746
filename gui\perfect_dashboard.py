"""
Perfect Dashboard for SFF Production System
Balanced fonts, clear layout, and readable design
"""

import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QMessageBox, QMenuBar, QAction,
    QScrollArea
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class PerfectModuleCard(QFrame):
    """Perfect module card with balanced text"""
    def __init__(self, title, description, icon="📋"):
        super().__init__()
        self.title = title
        self.init_ui(title, description, icon)
    
    def init_ui(self, title, description, icon):
        """Initialize perfect card UI"""
        self.setObjectName("moduleCard")
        self.setFixedSize(300, 150)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(12)
        
        # Icon and title
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("cardIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setObjectName("cardDescription")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Button
        button = QPushButton("Open Module")
        button.setObjectName("cardButton")
        button.clicked.connect(lambda: self.open_module(title))
        layout.addWidget(button)
    
    def open_module(self, module_name):
        """Open module"""
        parent = self.parent()
        while parent and not hasattr(parent, 'open_module'):
            parent = parent.parent()
        if parent:
            parent.open_module(module_name)

class PerfectDashboard(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize perfect dashboard interface"""
        self.setWindowTitle("SFF Production & Quality Management System - Dashboard")
        self.setGeometry(100, 100, 1200, 800)
        
        # Apply balanced design
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QWidget {
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #headerFrame {
                background-color: #228B22;
                border: none;
                padding: 25px;
            }
            #headerTitle {
                font-size: 26px;
                font-weight: bold;
                color: white;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #headerSubtitle {
                font-size: 16px;
                color: #e8f5e8;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                margin-top: 8px;
            }
            #welcomeSection {
                background-color: white;
                border: 2px solid #228B22;
                border-radius: 8px;
                padding: 25px;
                margin: 20px;
            }
            #welcomeTitle {
                font-size: 22px;
                font-weight: bold;
                color: #228B22;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #welcomeText {
                font-size: 15px;
                color: #333;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                margin-top: 12px;
                line-height: 1.4;
            }
            #modulesSection {
                background-color: white;
                border: 2px solid #228B22;
                border-radius: 8px;
                margin: 20px;
                padding: 25px;
            }
            #sectionTitle {
                font-size: 24px;
                font-weight: bold;
                color: #228B22;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                margin-bottom: 20px;
            }
            #moduleCard {
                background-color: #f8fff8;
                border: 2px solid #90EE90;
                border-radius: 8px;
                margin: 8px;
            }
            #moduleCard:hover {
                border-color: #228B22;
                background-color: #f0fff0;
            }
            #cardIcon {
                font-size: 24px;
                color: #228B22;
            }
            #cardTitle {
                font-size: 16px;
                font-weight: bold;
                color: #228B22;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #cardDescription {
                font-size: 13px;
                color: #333;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                line-height: 1.4;
            }
            #cardButton {
                background-color: #228B22;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            #cardButton:hover {
                background-color: #32CD32;
            }
            #statusBar {
                background-color: white;
                border-top: 2px solid #228B22;
                color: #228B22;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QMenuBar {
                background-color: #228B22;
                color: white;
                border: none;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: bold;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                color: white;
            }
            QMenuBar::item:selected {
                background-color: #32CD32;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Center window
        self.center_window()
        
        # Create main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # Main layout
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header section
        self.create_header(main_layout)
        
        # Content area with scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 25)
        content_layout.setSpacing(0)
        
        # Welcome section
        self.create_welcome_section(content_layout)
        
        # Modules section
        self.create_modules_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # Status bar
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        logout_action = QAction('Logout', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        
        settings_action = QAction('System Settings', self)
        settings_action.triggered.connect(lambda: self.open_module('System Settings'))
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_header(self, layout):
        """Create header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Left side - title
        title_layout = QVBoxLayout()
        title_layout.setSpacing(8)
        
        title = QLabel("SFF Production & Quality Management System")
        title.setObjectName("headerTitle")
        title_layout.addWidget(title)
        
        subtitle = QLabel("Dashboard - System Administrator")
        subtitle.setObjectName("headerSubtitle")
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Right side - user info
        user_info = QLabel("Welcome, Administrator")
        user_info.setStyleSheet("color: white; font-size: 18px; font-weight: bold; font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;")
        header_layout.addWidget(user_info)
        
        layout.addWidget(header_frame)
    
    def create_welcome_section(self, layout):
        """Create welcome section"""
        welcome_frame = QFrame()
        welcome_frame.setObjectName("welcomeSection")
        
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setSpacing(12)
        
        welcome_title = QLabel("Welcome to SFF Production System")
        welcome_title.setObjectName("welcomeTitle")
        welcome_layout.addWidget(welcome_title)
        
        welcome_text = QLabel("Manage your production and quality processes efficiently. Select a module below to get started.")
        welcome_text.setObjectName("welcomeText")
        welcome_text.setWordWrap(True)
        welcome_layout.addWidget(welcome_text)
        
        layout.addWidget(welcome_frame)
    
    def create_modules_section(self, layout):
        """Create modules section"""
        modules_frame = QFrame()
        modules_frame.setObjectName("modulesSection")
        
        modules_layout = QVBoxLayout(modules_frame)
        modules_layout.setSpacing(18)
        
        # Section title
        section_title = QLabel("System Modules")
        section_title.setObjectName("sectionTitle")
        modules_layout.addWidget(section_title)
        
        # Modules grid
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)
        grid_layout.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        
        # Define modules with icons
        modules = [
            ("User Management", "Manage system users and permissions", "👥"),
            ("Inventory Management", "Track materials and stock levels", "📦"),
            ("Recipe Management", "Create and manage production recipes", "📋"),
            ("Sample Batches", "Test production batches", "🧪"),
            ("Production Management", "Manage production orders", "🏭"),
            ("Quality Assurance", "QA tests and quality documents", "✅"),
            ("Reports & Analytics", "Generate system reports", "📊"),
            ("System Settings", "Configure system settings", "⚙️")
        ]
        
        # Create module cards in grid (3 per row)
        row, col = 0, 0
        for title, description, icon in modules:
            card = PerfectModuleCard(title, description, icon)
            grid_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 3:  # 3 cards per row
                col = 0
                row += 1
        
        modules_layout.addLayout(grid_layout)
        layout.addWidget(modules_frame)
    
    def create_status_bar(self):
        """Create status bar"""
        status_bar = self.statusBar()
        status_bar.setObjectName("statusBar")
        status_bar.showMessage("System Status: Online | Database: Connected | User: Administrator")
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def open_module(self, module_name):
        """Open specific module"""
        QMessageBox.information(
            self,
            "Module",
            f"Opening {module_name}...\n\n"
            f"This module will be available in the next update.\n\n"
            f"Features coming soon!"
        )
    
    def logout(self):
        """Logout and return to login"""
        reply = QMessageBox.question(
            self,
            "Logout Confirmation",
            "Are you sure you want to logout?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            
            # Show login window again
            try:
                from gui.perfect_login import PerfectLoginWindow
                self.login_window = PerfectLoginWindow()
                self.login_window.show()
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to open login window: {str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About SFF Production System",
            "SFF Production & Quality Management System\n"
            "Version 1.0.0\n\n"
            "A comprehensive production and quality management system\n"
            "designed for manufacturing operations.\n\n"
            "Key Features:\n"
            "• User Management with Role-based Access\n"
            "• Inventory Management & Stock Control\n"
            "• Recipe Management & Cost Calculation\n"
            "• Production Planning & Scheduling\n"
            "• Quality Assurance & Testing\n"
            "• Comprehensive Reporting & Analytics\n\n"
            "All data is securely stored in Microsoft Access database.\n\n"
            "© 2024 SFF Production Systems"
        )

def main():
    """Run the perfect dashboard"""
    app = QApplication(sys.argv)
    
    dashboard = PerfectDashboard()
    dashboard.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
