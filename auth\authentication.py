"""
SFF Production & Quality Management System
Authentication and Security Module
"""

import bcrypt
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from database.db_manager import DatabaseManager
from models.data_models import User
from config.settings import (
    PASSWORD_MIN_LENGTH, MAX_LOGIN_ATTEMPTS, 
    LOCKOUT_DURATION, SESSION_TIMEOUT, ROLE_PERMISSIONS
)

class AuthenticationManager:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.current_user = None
        self.session_start_time = None
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except:
            return False
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password strength"""
        result = {
            'is_valid': True,
            'errors': []
        }
        
        if len(password) < PASSWORD_MIN_LENGTH:
            result['is_valid'] = False
            result['errors'].append(f"Password must be at least {PASSWORD_MIN_LENGTH} characters long")
        
        if not any(c.isupper() for c in password):
            result['is_valid'] = False
            result['errors'].append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            result['is_valid'] = False
            result['errors'].append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            result['is_valid'] = False
            result['errors'].append("Password must contain at least one number")
        
        return result
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user login"""
        result = {
            'success': False,
            'user': None,
            'message': '',
            'locked_until': None
        }
        
        # Get user from database
        user = self.db_manager.get_user_by_username(username)
        
        if not user:
            result['message'] = "Invalid username or password"
            return result
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now():
            result['message'] = f"Account is locked until {user.locked_until.strftime('%Y-%m-%d %H:%M:%S')}"
            result['locked_until'] = user.locked_until
            return result
        
        # Check if account is active
        if not user.is_active:
            result['message'] = "Account is disabled"
            return result
        
        # Verify password
        if self.verify_password(password, user.password_hash):
            # Successful login
            self.current_user = user
            self.session_start_time = datetime.now()
            
            # Reset failed login attempts
            self.db_manager.update_failed_login_attempts(username, 0, None)
            
            # Update last login
            self.db_manager.update_last_login(user.user_id)
            
            # Log successful login
            self.db_manager.log_action(
                user.user_id, 
                "LOGIN_SUCCESS", 
                "Users", 
                user.user_id,
                "", 
                f"Successful login at {datetime.now()}"
            )
            
            result['success'] = True
            result['user'] = user
            result['message'] = "Login successful"
        else:
            # Failed login
            failed_attempts = user.failed_login_attempts + 1
            locked_until = None
            
            if failed_attempts >= MAX_LOGIN_ATTEMPTS:
                locked_until = datetime.now() + timedelta(seconds=LOCKOUT_DURATION)
                result['message'] = f"Account locked due to {MAX_LOGIN_ATTEMPTS} failed attempts. Try again after {LOCKOUT_DURATION // 60} minutes."
                result['locked_until'] = locked_until
            else:
                remaining_attempts = MAX_LOGIN_ATTEMPTS - failed_attempts
                result['message'] = f"Invalid password. {remaining_attempts} attempts remaining."
            
            # Update failed login attempts
            self.db_manager.update_failed_login_attempts(username, failed_attempts, locked_until)
            
            # Log failed login
            self.db_manager.log_action(
                user.user_id if user else 0,
                "LOGIN_FAILED",
                "Users",
                user.user_id if user else None,
                "",
                f"Failed login attempt for {username} at {datetime.now()}"
            )
        
        return result
    
    def logout(self):
        """Logout current user"""
        if self.current_user:
            # Log logout
            self.db_manager.log_action(
                self.current_user.user_id,
                "LOGOUT",
                "Users",
                self.current_user.user_id,
                "",
                f"User logged out at {datetime.now()}"
            )
            
            self.current_user = None
            self.session_start_time = None
    
    def is_session_valid(self) -> bool:
        """Check if current session is still valid"""
        if not self.current_user or not self.session_start_time:
            return False
        
        session_duration = (datetime.now() - self.session_start_time).total_seconds()
        return session_duration < SESSION_TIMEOUT
    
    def extend_session(self):
        """Extend current session"""
        if self.current_user:
            self.session_start_time = datetime.now()
    
    def has_permission(self, permission: str) -> bool:
        """Check if current user has specific permission"""
        if not self.current_user:
            return False
        
        user_permissions = ROLE_PERMISSIONS.get(self.current_user.role, [])
        
        # Admin has all permissions
        if 'all' in user_permissions:
            return True
        
        return permission in user_permissions
    
    def require_permission(self, permission: str) -> bool:
        """Require specific permission, extend session if valid"""
        if not self.is_session_valid():
            return False
        
        if not self.has_permission(permission):
            return False
        
        self.extend_session()
        return True
    
    def get_current_user(self) -> Optional[User]:
        """Get current authenticated user"""
        if self.is_session_valid():
            return self.current_user
        return None
    
    def change_password(self, old_password: str, new_password: str) -> Dict[str, Any]:
        """Change current user's password"""
        result = {
            'success': False,
            'message': ''
        }
        
        if not self.current_user:
            result['message'] = "No user logged in"
            return result
        
        # Verify old password
        if not self.verify_password(old_password, self.current_user.password_hash):
            result['message'] = "Current password is incorrect"
            return result
        
        # Validate new password
        validation = self.validate_password_strength(new_password)
        if not validation['is_valid']:
            result['message'] = "; ".join(validation['errors'])
            return result
        
        # Hash new password
        new_password_hash = self.hash_password(new_password)
        
        # Update password in database
        query = "UPDATE Users SET PasswordHash = ? WHERE UserID = ?"
        if self.db_manager.execute_non_query(query, (new_password_hash, self.current_user.user_id)):
            # Log password change
            self.db_manager.log_action(
                self.current_user.user_id,
                "PASSWORD_CHANGE",
                "Users",
                self.current_user.user_id,
                "",
                f"Password changed at {datetime.now()}"
            )
            
            result['success'] = True
            result['message'] = "Password changed successfully"
        else:
            result['message'] = "Failed to update password"
        
        return result
    
    def create_user(self, username: str, password: str, email: str, full_name: str, role: str) -> Dict[str, Any]:
        """Create new user (admin only)"""
        result = {
            'success': False,
            'message': ''
        }
        
        # Check admin permission
        if not self.require_permission('all'):
            result['message'] = "Insufficient permissions"
            return result
        
        # Validate password
        validation = self.validate_password_strength(password)
        if not validation['is_valid']:
            result['message'] = "; ".join(validation['errors'])
            return result
        
        # Check if username already exists
        existing_user = self.db_manager.get_user_by_username(username)
        if existing_user:
            result['message'] = "Username already exists"
            return result
        
        # Create user
        user = User(
            username=username,
            password_hash=self.hash_password(password),
            email=email,
            full_name=full_name,
            role=role,
            is_active=True
        )
        
        if self.db_manager.create_user(user):
            # Log user creation
            self.db_manager.log_action(
                self.current_user.user_id,
                "USER_CREATE",
                "Users",
                None,
                "",
                f"Created user {username} with role {role}"
            )
            
            result['success'] = True
            result['message'] = "User created successfully"
        else:
            result['message'] = "Failed to create user"
        
        return result
