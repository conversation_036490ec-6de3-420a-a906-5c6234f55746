"""
SFF Production & Quality Management System
Database Manager - Handles all database operations
"""

import pyodbc
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from config.settings import DATABASE_PATH
from models.data_models import *

class DatabaseManager:
    def __init__(self):
        self.connection_string = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={DATABASE_PATH};'
        self.connection = None
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = pyodbc.connect(self.connection_string)
            return True
        except Exception as e:
            print(f"Database connection error: {str(e)}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute SELECT query and return results"""
        if not self.connection:
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
        except Exception as e:
            print(f"Query execution error: {str(e)}")
            return []
    
    def execute_non_query(self, query: str, params: tuple = None) -> bool:
        """Execute INSERT, UPDATE, DELETE queries"""
        if not self.connection:
            if not self.connect():
                return False
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return True
        except Exception as e:
            print(f"Non-query execution error: {str(e)}")
            self.connection.rollback()
            return False
    
    def get_last_insert_id(self) -> Optional[int]:
        """Get the last inserted record ID"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT @@IDENTITY")
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            print(f"Error getting last insert ID: {str(e)}")
            return None
    
    # User Management Methods
    def authenticate_user(self, username: str, password_hash: str) -> Optional[User]:
        """Authenticate user login"""
        query = """
            SELECT UserID, Username, PasswordHash, Email, FullName, Role, IsActive,
                   CreatedDate, LastLogin, FailedLoginAttempts, LockedUntil
            FROM Users 
            WHERE Username = ? AND PasswordHash = ? AND IsActive = True
        """
        results = self.execute_query(query, (username, password_hash))
        
        if results:
            row = results[0]
            return User(
                user_id=row['UserID'],
                username=row['Username'],
                password_hash=row['PasswordHash'],
                email=row['Email'],
                full_name=row['FullName'],
                role=row['Role'],
                is_active=row['IsActive'],
                created_date=row['CreatedDate'],
                last_login=row['LastLogin'],
                failed_login_attempts=row['FailedLoginAttempts'],
                locked_until=row['LockedUntil']
            )
        return None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        query = """
            SELECT UserID, Username, PasswordHash, Email, FullName, Role, IsActive,
                   CreatedDate, LastLogin, FailedLoginAttempts, LockedUntil
            FROM Users 
            WHERE Username = ?
        """
        results = self.execute_query(query, (username,))
        
        if results:
            row = results[0]
            return User(
                user_id=row['UserID'],
                username=row['Username'],
                password_hash=row['PasswordHash'],
                email=row['Email'],
                full_name=row['FullName'],
                role=row['Role'],
                is_active=row['IsActive'],
                created_date=row['CreatedDate'],
                last_login=row['LastLogin'],
                failed_login_attempts=row['FailedLoginAttempts'],
                locked_until=row['LockedUntil']
            )
        return None
    
    def update_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp"""
        query = "UPDATE Users SET LastLogin = ? WHERE UserID = ?"
        return self.execute_non_query(query, (datetime.now(), user_id))
    
    def update_failed_login_attempts(self, username: str, attempts: int, locked_until: datetime = None) -> bool:
        """Update failed login attempts and lock status"""
        query = "UPDATE Users SET FailedLoginAttempts = ?, LockedUntil = ? WHERE Username = ?"
        return self.execute_non_query(query, (attempts, locked_until, username))
    
    def create_user(self, user: User) -> bool:
        """Create new user"""
        query = """
            INSERT INTO Users (Username, PasswordHash, Email, FullName, Role, IsActive)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        return self.execute_non_query(query, (
            user.username, user.password_hash, user.email, 
            user.full_name, user.role, user.is_active
        ))
    
    def get_all_users(self) -> List[User]:
        """Get all users"""
        query = """
            SELECT UserID, Username, PasswordHash, Email, FullName, Role, IsActive,
                   CreatedDate, LastLogin, FailedLoginAttempts, LockedUntil
            FROM Users 
            ORDER BY FullName
        """
        results = self.execute_query(query)
        
        users = []
        for row in results:
            users.append(User(
                user_id=row['UserID'],
                username=row['Username'],
                password_hash=row['PasswordHash'],
                email=row['Email'],
                full_name=row['FullName'],
                role=row['Role'],
                is_active=row['IsActive'],
                created_date=row['CreatedDate'],
                last_login=row['LastLogin'],
                failed_login_attempts=row['FailedLoginAttempts'],
                locked_until=row['LockedUntil']
            ))
        
        return users
    
    # Material Management Methods
    def get_all_materials(self) -> List[Material]:
        """Get all materials"""
        query = """
            SELECT MaterialID, MaterialCode, MaterialName, MaterialType, UnitOfMeasure,
                   CurrentStock, MinimumThreshold, UnitCost, Supplier, StorageLocation,
                   ExpiryDate, CreatedDate, IsActive
            FROM Materials 
            WHERE IsActive = True
            ORDER BY MaterialName
        """
        results = self.execute_query(query)
        
        materials = []
        for row in results:
            materials.append(Material(
                material_id=row['MaterialID'],
                material_code=row['MaterialCode'],
                material_name=row['MaterialName'],
                material_type=row['MaterialType'],
                unit_of_measure=row['UnitOfMeasure'],
                current_stock=row['CurrentStock'],
                minimum_threshold=row['MinimumThreshold'],
                unit_cost=row['UnitCost'],
                supplier=row['Supplier'],
                storage_location=row['StorageLocation'],
                expiry_date=row['ExpiryDate'],
                created_date=row['CreatedDate'],
                is_active=row['IsActive']
            ))
        
        return materials
    
    def create_material(self, material: Material) -> bool:
        """Create new material"""
        query = """
            INSERT INTO Materials (MaterialCode, MaterialName, MaterialType, UnitOfMeasure,
                                 CurrentStock, MinimumThreshold, UnitCost, Supplier, 
                                 StorageLocation, ExpiryDate, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        return self.execute_non_query(query, (
            material.material_code, material.material_name, material.material_type,
            material.unit_of_measure, material.current_stock, material.minimum_threshold,
            material.unit_cost, material.supplier, material.storage_location,
            material.expiry_date, material.is_active
        ))
    
    def get_low_stock_materials(self) -> List[Material]:
        """Get materials with low stock"""
        query = """
            SELECT MaterialID, MaterialCode, MaterialName, MaterialType, UnitOfMeasure,
                   CurrentStock, MinimumThreshold, UnitCost, Supplier, StorageLocation,
                   ExpiryDate, CreatedDate, IsActive
            FROM Materials 
            WHERE IsActive = True AND CurrentStock <= MinimumThreshold
            ORDER BY CurrentStock
        """
        results = self.execute_query(query)
        
        materials = []
        for row in results:
            materials.append(Material(
                material_id=row['MaterialID'],
                material_code=row['MaterialCode'],
                material_name=row['MaterialName'],
                material_type=row['MaterialType'],
                unit_of_measure=row['UnitOfMeasure'],
                current_stock=row['CurrentStock'],
                minimum_threshold=row['MinimumThreshold'],
                unit_cost=row['UnitCost'],
                supplier=row['Supplier'],
                storage_location=row['StorageLocation'],
                expiry_date=row['ExpiryDate'],
                created_date=row['CreatedDate'],
                is_active=row['IsActive']
            ))
        
        return materials
    
    # Recipe Management Methods
    def get_all_recipes(self) -> List[Recipe]:
        """Get all active recipes"""
        query = """
            SELECT r.RecipeID, r.RecipeCode, r.RecipeName, r.Version, r.Description,
                   r.BatchSize, r.UnitOfMeasure, r.TotalCost, r.IsApproved,
                   r.CreatedBy, r.CreatedDate, r.ApprovedBy, r.ApprovedDate, r.IsActive,
                   u1.FullName as CreatedByName, u2.FullName as ApprovedByName
            FROM Recipes r
            LEFT JOIN Users u1 ON r.CreatedBy = u1.UserID
            LEFT JOIN Users u2 ON r.ApprovedBy = u2.UserID
            WHERE r.IsActive = True
            ORDER BY r.RecipeName
        """
        results = self.execute_query(query)

        recipes = []
        for row in results:
            recipes.append(Recipe(
                recipe_id=row['RecipeID'],
                recipe_code=row['RecipeCode'],
                recipe_name=row['RecipeName'],
                version=row['Version'],
                description=row['Description'],
                batch_size=row['BatchSize'],
                unit_of_measure=row['UnitOfMeasure'],
                total_cost=row['TotalCost'],
                is_approved=row['IsApproved'],
                created_by=row['CreatedBy'],
                created_date=row['CreatedDate'],
                approved_by=row['ApprovedBy'],
                approved_date=row['ApprovedDate'],
                is_active=row['IsActive']
            ))

        return recipes

    def create_recipe(self, recipe: Recipe) -> Optional[int]:
        """Create new recipe and return recipe ID"""
        query = """
            INSERT INTO Recipes (RecipeCode, RecipeName, Version, Description, BatchSize,
                               UnitOfMeasure, TotalCost, IsApproved, CreatedBy, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        if self.execute_non_query(query, (
            recipe.recipe_code, recipe.recipe_name, recipe.version, recipe.description,
            recipe.batch_size, recipe.unit_of_measure, recipe.total_cost,
            recipe.is_approved, recipe.created_by, recipe.is_active
        )):
            return self.get_last_insert_id()
        return None

    def get_recipe_ingredients(self, recipe_id: int) -> List[RecipeIngredient]:
        """Get ingredients for a specific recipe"""
        query = """
            SELECT ri.RecipeIngredientID, ri.RecipeID, ri.MaterialID, ri.Quantity,
                   ri.UnitOfMeasure, ri.Percentage, ri.Notes,
                   m.MaterialName, m.MaterialCode, m.UnitCost
            FROM RecipeIngredients ri
            JOIN Materials m ON ri.MaterialID = m.MaterialID
            WHERE ri.RecipeID = ?
            ORDER BY ri.RecipeIngredientID
        """
        results = self.execute_query(query, (recipe_id,))

        ingredients = []
        for row in results:
            ingredients.append(RecipeIngredient(
                recipe_ingredient_id=row['RecipeIngredientID'],
                recipe_id=row['RecipeID'],
                material_id=row['MaterialID'],
                quantity=row['Quantity'],
                unit_of_measure=row['UnitOfMeasure'],
                percentage=row['Percentage'],
                notes=row['Notes']
            ))

        return ingredients

    def check_recipe_availability(self, recipe_id: int, batch_quantity: Decimal) -> Dict[str, Any]:
        """Check if recipe can be produced with current inventory"""
        query = """
            SELECT ri.MaterialID, ri.Quantity, ri.UnitOfMeasure,
                   m.MaterialName, m.CurrentStock, m.UnitOfMeasure as MaterialUOM
            FROM RecipeIngredients ri
            JOIN Materials m ON ri.MaterialID = m.MaterialID
            WHERE ri.RecipeID = ?
        """
        results = self.execute_query(query, (recipe_id,))

        availability = {
            'can_produce': True,
            'missing_materials': [],
            'insufficient_materials': []
        }

        for row in results:
            required_qty = row['Quantity'] * batch_quantity
            available_qty = row['CurrentStock']

            if available_qty == 0:
                availability['missing_materials'].append({
                    'material_id': row['MaterialID'],
                    'material_name': row['MaterialName'],
                    'required': required_qty,
                    'available': available_qty
                })
                availability['can_produce'] = False
            elif available_qty < required_qty:
                availability['insufficient_materials'].append({
                    'material_id': row['MaterialID'],
                    'material_name': row['MaterialName'],
                    'required': required_qty,
                    'available': available_qty,
                    'shortage': required_qty - available_qty
                })
                availability['can_produce'] = False

        return availability

    # Audit Log Methods
    def log_action(self, user_id: int, action: str, table_name: str, record_id: int = None,
                   old_values: str = "", new_values: str = "", ip_address: str = "") -> bool:
        """Log user action for audit trail"""
        query = """
            INSERT INTO AuditLog (UserID, Action, TableName, RecordID, OldValues, NewValues, IPAddress)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return self.execute_non_query(query, (
            user_id, action, table_name, record_id, old_values, new_values, ip_address
        ))
